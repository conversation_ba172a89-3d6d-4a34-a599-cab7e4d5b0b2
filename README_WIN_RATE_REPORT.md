# 胜率统计报表系统

## 🎯 系统概述

这是一个完整的自动化胜率统计报表系统，专门为交易信号分析设计。系统能够：

- **自动计算胜率**: 基于信号结算数据自动计算多维度胜率统计
- **定时报表生成**: 每日0点重新计算，每小时发送统计报表
- **钉钉通知集成**: 自动将报表发送到钉钉群或个人
- **趋势分析**: 提供连胜连败、表现趋势等深度分析
- **数据导出**: 支持JSON格式的报表数据导出

## 🚀 快速开始

### 1. 安装依赖

```bash
pip3 install apscheduler
```

### 2. 检查环境

```bash
python3 start_win_rate_report.py --check
```

### 3. 配置钉钉Token

```bash
# 方法1: 通过命令行参数
python3 start_win_rate_report.py --token "YOUR_DINGTALK_WEBHOOK_URL"

# 方法2: 编辑配置文件
python3 start_win_rate_report.py --config
# 然后编辑 config/win_rate_report_config.json 文件
```

### 4. 启动系统

```bash
python3 start_win_rate_report.py
```

## 📊 功能特性

### 核心统计指标

- **胜率**: 成功交易数/总交易数
- **平均盈亏**: 所有交易的平均盈亏百分比
- **平均置信度**: 信号的平均置信度
- **一致性得分**: 基于胜率稳定性的综合评分
- **信号质量评级**: 优秀/良好/一般/需要改进

### 多维度分析

- **时间维度**: 今日、本周、本月统计
- **方向维度**: 看涨(UP)和看跌(DOWN)信号分别统计
- **强度维度**: 强(STRONG)、中(MEDIUM)、弱(WEAK)信号分别统计

### 定时任务

- **每日0点**: 重新计算当日胜率数据
- **每小时整点**: 发送胜率统计报表
- **系统监控**: 每30分钟进行健康检查

## 📱 通知示例

### 每小时报表
```
📊 胜率统计报表 - 14:00

🎯 今日表现
信号总数: 12
已结算: 10
胜率: 70.0%
总盈亏: +2.35%

📈 本周概览
信号总数: 45
胜率: 64.3%
总盈亏: +8.92%
```

### 每日总结
```
🌟 每日胜率总结 - 2025-07-17

🎯 今日完整统计
信号总数: 18
已结算数: 16
胜率: 68.8%
总盈亏: +4.25%

🏆 性能指标
一致性得分: 82.5/100
信号质量: 优秀
```

## 🔧 命令行工具

```bash
# 运行功能演示
python3 start_win_rate_report.py --demo

# 运行完整测试
python3 start_win_rate_report.py --test

# 检查系统环境
python3 start_win_rate_report.py --check

# 创建配置文件
python3 start_win_rate_report.py --config

# 指定Token启动
python3 start_win_rate_report.py --token "YOUR_TOKEN"
```

## ⚙️ 配置说明

配置文件位置: `config/win_rate_report_config.json`

主要配置项：
- `database.path`: 信号结算数据库路径
- `scheduler.daily_calculation_time`: 每日计算时间
- `scheduler.hourly_report_interval`: 每小时报表间隔
- `notification.dingtalk_token`: 钉钉Webhook URL
- `notification.min_signals_for_hourly`: 每小时报表最小信号数

## 📁 文件结构

```
├── win_rate_report_main.py              # 主程序入口
├── start_win_rate_report.py             # 启动脚本
├── config/
│   └── win_rate_report_config.json      # 配置文件
├── quant/strategies/
│   ├── win_rate_report_system.py        # 核心报表系统
│   ├── win_rate_report_scheduler.py     # 定时调度器
│   └── win_rate_report_notifier.py      # 钉钉通知器
├── tests/
│   ├── test_win_rate_report_system.py   # 功能测试
│   └── demo_win_rate_report_system.py   # 演示程序
├── logs/
│   └── win_rate_report.log              # 系统日志
└── exports/
    └── win_rate_report_*.json           # 导出的报表文件
```

## 🧪 测试验证

系统已通过完整的功能测试：

- ✅ 胜率统计计算功能
- ✅ 趋势分析功能
- ✅ 报表生成和导出
- ✅ 钉钉通知格式
- ✅ 定时任务调度
- ✅ 系统集成测试

运行测试：
```bash
python3 tests/test_win_rate_report_system.py
```

运行演示：
```bash
python3 tests/demo_win_rate_report_system.py
```

## 📞 使用说明

1. **确保数据源**: 系统依赖 `./data/signal_settlement.db` 数据库，请确保交易系统正常运行并生成信号数据

2. **配置钉钉Token**: 在钉钉群中创建机器人，获取Webhook URL并配置到系统中

3. **调整时间设置**: 根据需要调整每日计算时间和报表发送频率

4. **监控日志**: 查看 `./logs/win_rate_report.log` 获取详细运行日志

5. **数据导出**: 系统会自动将报表导出到 `./exports/` 目录

## 🔍 故障排除

- **数据库连接失败**: 检查数据库文件是否存在，确认路径配置正确
- **钉钉通知失败**: 验证Webhook URL是否正确，检查网络连接
- **调度任务不执行**: 确认系统时区设置，查看日志获取详细错误信息

---

*本系统已完成开发和测试，可以直接投入使用。如有问题请查看日志文件或运行测试程序进行诊断。*
