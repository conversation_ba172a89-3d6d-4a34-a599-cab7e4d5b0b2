#!/usr/bin/env python3
"""
胜率统计报表系统启动脚本
简化的启动入口，提供基本的命令行参数支持

Author: AI Assistant
Date: 2025-07-17
"""

import sys
import os
import argparse
import json
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def check_dependencies():
    """检查依赖包"""
    missing_deps = []
    
    try:
        import apscheduler
    except ImportError:
        missing_deps.append("apscheduler")
    
    if missing_deps:
        print("❌ 缺少必要的依赖包:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装:")
        print(f"   pip install {' '.join(missing_deps)}")
        return False
    
    return True


def check_database():
    """检查数据库文件"""
    db_path = "./data/signal_settlement.db"
    if not os.path.exists(db_path):
        print(f"⚠️  警告: 信号结算数据库不存在: {db_path}")
        print("   系统将尝试连接数据库，如果数据库不存在可能会出错")
        print("   请确保交易系统已运行并生成了信号数据")
        return False
    return True


def setup_config(dingtalk_token=None):
    """设置配置文件"""
    config_file = "./config/win_rate_report_config.json"
    
    # 确保配置目录存在
    os.makedirs(os.path.dirname(config_file), exist_ok=True)
    
    # 如果配置文件不存在，创建默认配置
    if not os.path.exists(config_file):
        default_config = {
            "database": {
                "path": "./data/signal_settlement.db"
            },
            "scheduler": {
                "enable_daily_calculation": True,
                "enable_hourly_reports": True,
                "daily_calculation_time": "00:00",
                "hourly_report_interval": 1,
                "timezone": "Asia/Shanghai",
                "max_workers": 3,
                "send_empty_reports": False,
                "min_signals_for_report": 1,
                "max_retries": 3,
                "retry_interval": 300
            },
            "notification": {
                "dingtalk_token": dingtalk_token,
                "enable_hourly_reports": True,
                "enable_daily_summary": True,
                "enable_error_alerts": True,
                "min_signals_for_hourly": 1,
                "min_signals_for_daily": 5,
                "use_emoji": True,
                "show_detailed_stats": True,
                "show_trend_analysis": True
            },
            "logging": {
                "level": "INFO",
                "file": "./logs/win_rate_report.log"
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建默认配置文件: {config_file}")
    
    # 如果提供了钉钉Token，更新配置
    if dingtalk_token:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['notification']['dingtalk_token'] = dingtalk_token
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已更新钉钉Token配置")
    
    return config_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="胜率统计报表系统启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 start_win_rate_report.py                    # 使用默认配置启动
  python3 start_win_rate_report.py --token YOUR_TOKEN # 指定钉钉Token启动
  python3 start_win_rate_report.py --demo             # 运行演示程序
  python3 start_win_rate_report.py --test             # 运行测试程序
  python3 start_win_rate_report.py --config           # 仅创建配置文件
        """
    )
    
    parser.add_argument(
        '--token', '-t',
        help='钉钉Webhook Token'
    )
    
    parser.add_argument(
        '--config', '-c',
        action='store_true',
        help='仅创建配置文件，不启动系统'
    )
    
    parser.add_argument(
        '--demo', '-d',
        action='store_true',
        help='运行演示程序'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='运行测试程序'
    )
    
    parser.add_argument(
        '--check',
        action='store_true',
        help='检查系统环境和依赖'
    )
    
    args = parser.parse_args()
    
    print("🚀 胜率统计报表系统启动脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 确保必要目录存在
    for directory in ['./logs', './config', './data', './exports']:
        os.makedirs(directory, exist_ok=True)
    
    # 检查环境
    if args.check:
        print("\n🔍 系统环境检查:")
        print(f"   Python版本: {sys.version}")
        print(f"   工作目录: {os.getcwd()}")
        
        # 检查数据库
        db_exists = check_database()
        print(f"   数据库状态: {'✅ 存在' if db_exists else '⚠️  不存在'}")
        
        # 检查配置文件
        config_exists = os.path.exists("./config/win_rate_report_config.json")
        print(f"   配置文件: {'✅ 存在' if config_exists else '❌ 不存在'}")
        
        print("\n✅ 环境检查完成")
        return
    
    # 设置配置
    config_file = setup_config(args.token)
    
    # 仅创建配置
    if args.config:
        print(f"\n✅ 配置文件已创建: {config_file}")
        print("\n💡 下一步:")
        print("   1. 编辑配置文件设置钉钉Token")
        print("   2. 运行 python3 start_win_rate_report.py 启动系统")
        return
    
    # 运行演示
    if args.demo:
        print("\n🎯 启动演示程序...")
        os.system("python3 tests/demo_win_rate_report_system.py")
        return
    
    # 运行测试
    if args.test:
        print("\n🧪 启动测试程序...")
        os.system("python3 tests/test_win_rate_report_system.py")
        return
    
    # 检查钉钉Token配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    if not config['notification']['dingtalk_token']:
        print("\n⚠️  警告: 未配置钉钉Token")
        print("   系统将启动但无法发送通知")
        print("   请使用 --token 参数指定Token，或编辑配置文件")
        
        response = input("\n是否继续启动? (y/N): ")
        if response.lower() != 'y':
            print("启动已取消")
            return
    
    # 检查数据库
    if not check_database():
        response = input("\n是否继续启动? (y/N): ")
        if response.lower() != 'y':
            print("启动已取消")
            return
    
    # 启动主程序
    print("\n🚀 启动胜率统计报表系统...")
    print("   配置文件:", config_file)
    print("   按 Ctrl+C 停止系统")
    print("-" * 50)
    
    try:
        # 导入并运行主程序
        import asyncio
        from win_rate_report_main import WinRateReportApp
        
        async def run_app():
            app = WinRateReportApp(config_file)
            await app.start()
        
        asyncio.run(run_app())
        
    except KeyboardInterrupt:
        print("\n\n👋 系统已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
