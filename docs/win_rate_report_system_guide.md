# 胜率统计报表系统使用指南

## 📋 概述

胜率统计报表系统是一个自动化的交易信号胜率统计和报表生成系统，具备以下核心功能：

- **自动胜率计算**: 基于信号结算数据自动计算各种维度的胜率统计
- **定时报表生成**: 每日0点重新计算数据，每小时发送统计报表
- **钉钉通知集成**: 自动将报表发送到钉钉群或个人
- **多维度分析**: 支持按方向、强度、时间周期等多维度统计分析
- **趋势分析**: 提供连胜连败、表现趋势等深度分析

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install apscheduler

# 检查环境
python3 start_win_rate_report.py --check
```

### 2. 配置系统

```bash
# 创建默认配置文件
python3 start_win_rate_report.py --config

# 或者直接指定钉钉Token创建配置
python3 start_win_rate_report.py --token "YOUR_DINGTALK_WEBHOOK_URL" --config
```

### 3. 启动系统

```bash
# 使用默认配置启动
python3 start_win_rate_report.py

# 指定钉钉Token启动
python3 start_win_rate_report.py --token "YOUR_DINGTALK_WEBHOOK_URL"
```

### 4. 运行演示和测试

```bash
# 运行功能演示
python3 start_win_rate_report.py --demo

# 运行完整测试
python3 start_win_rate_report.py --test
```

## ⚙️ 配置说明

配置文件位置: `config/win_rate_report_config.json`

### 数据库配置
```json
{
  "database": {
    "path": "./data/signal_settlement.db"  // 信号结算数据库路径
  }
}
```

### 调度器配置
```json
{
  "scheduler": {
    "enable_daily_calculation": true,      // 启用每日数据重新计算
    "enable_hourly_reports": true,         // 启用每小时报表发送
    "daily_calculation_time": "00:00",     // 每日计算时间
    "hourly_report_interval": 1,           // 每小时报表间隔
    "timezone": "Asia/Shanghai",           // 时区设置
    "send_empty_reports": false,           // 是否发送空报表
    "min_signals_for_report": 1            // 发送报表的最小信号数
  }
}
```

### 通知配置
```json
{
  "notification": {
    "dingtalk_token": "YOUR_WEBHOOK_URL",  // 钉钉Webhook URL
    "enable_hourly_reports": true,         // 启用每小时报表
    "enable_daily_summary": true,          // 启用每日总结
    "enable_error_alerts": true,           // 启用错误警报
    "min_signals_for_hourly": 1,          // 每小时报表最小信号数
    "min_signals_for_daily": 5,           // 每日总结最小信号数
    "use_emoji": true,                     // 使用表情符号
    "show_detailed_stats": true,           // 显示详细统计
    "show_trend_analysis": true            // 显示趋势分析
  }
}
```

## 📊 功能特性

### 1. 胜率统计维度

- **时间维度**: 今日、本周、本月统计
- **方向维度**: 看涨(UP)和看跌(DOWN)信号分别统计
- **强度维度**: 强(STRONG)、中(MEDIUM)、弱(WEAK)信号分别统计
- **结果维度**: 胜利(WIN)、失败(LOSS)、平局(TIE)统计

### 2. 关键指标

- **胜率**: 成功交易数/总交易数
- **平均盈亏**: 所有交易的平均盈亏百分比
- **平均置信度**: 信号的平均置信度
- **最佳/最差信号**: 单笔最大盈利和亏损
- **一致性得分**: 基于胜率稳定性的综合评分
- **信号质量评级**: 优秀/良好/一般/需要改进

### 3. 趋势分析

- **最近表现**: 最近N笔交易的胜率表现
- **连胜连败**: 最大连续胜利和失败次数
- **表现趋势**: 优秀/良好/平稳/不佳
- **操作建议**: 基于表现给出的操作建议

### 4. 定时任务

- **每日0点**: 重新计算当日胜率数据
- **每小时整点**: 发送胜率统计报表
- **系统监控**: 每30分钟进行健康检查

## 📱 通知格式

### 每小时报表示例
```
📊 胜率统计报表 - 14:00

📅 统计时间: 2025-07-17 14:00

🎯 今日表现
信号总数: 12
已结算: 10
胜率: 70.0%
总盈亏: +2.35%

📈 本周概览
信号总数: 45
胜率: 64.3%
总盈亏: +8.92%

📋 详细分析
看涨胜率: 75.0%
看跌胜率: 65.0%
强信号胜率: 85.0%

🔍 趋势分析
表现趋势: 表现优秀
操作建议: 继续保持当前策略
```

### 每日总结示例
```
🌟 每日胜率总结 - 2025-07-17

🎯 今日完整统计
信号总数: 18
已结算数: 16
胜率: 68.8%
总盈亏: +4.25%

📊 历史对比
本周胜率: 64.3%
本月胜率: 61.7%

🏆 性能指标
一致性得分: 82.5/100
信号质量: 优秀

🔍 趋势分析
最近表现: 最近15笔交易胜率73.3%
最大连胜: 4次
最大连败: 2次
操作建议: 继续保持当前策略

🎉 每日总结
今日表现优秀！

明日继续加油！💪
```

## 🔧 高级用法

### 1. 程序化调用

```python
from quant.strategies.win_rate_report_system import WinRateReportSystem

# 初始化系统
report_system = WinRateReportSystem("./data/signal_settlement.db")

# 计算今日胜率
today_stats = report_system.calculate_win_rate_stats(1)
print(f"今日胜率: {today_stats.win_rate}%")

# 生成综合报表
report = report_system.generate_comprehensive_report()

# 导出报表
export_file = report_system.export_report_to_json(report)
```

### 2. 自定义调度

```python
from quant.strategies.win_rate_report_scheduler import WinRateReportScheduler, SchedulerConfig

# 自定义配置
config = SchedulerConfig(
    daily_calculation_time="01:00",  # 凌晨1点计算
    hourly_report_interval=2,        # 每2小时发送
    min_signals_for_report=3         # 至少3个信号
)

# 创建调度器
scheduler = WinRateReportScheduler(report_system, config, notification_callback)
scheduler.start()
```

### 3. 自定义通知

```python
from quant.strategies.win_rate_report_notifier import WinRateReportNotifier, WinRateNotificationConfig

# 自定义通知配置
config = WinRateNotificationConfig(
    use_emoji=False,              # 不使用表情符号
    show_detailed_stats=False,    # 不显示详细统计
    min_signals_for_hourly=5      # 每小时至少5个信号
)

notifier = WinRateReportNotifier(config)
```

## 📁 文件结构

```
├── win_rate_report_main.py              # 主程序入口
├── start_win_rate_report.py             # 启动脚本
├── config/
│   └── win_rate_report_config.json      # 配置文件
├── quant/strategies/
│   ├── win_rate_report_system.py        # 核心报表系统
│   ├── win_rate_report_scheduler.py     # 定时调度器
│   └── win_rate_report_notifier.py      # 钉钉通知器
├── tests/
│   ├── test_win_rate_report_system.py   # 功能测试
│   └── demo_win_rate_report_system.py   # 演示程序
├── logs/
│   └── win_rate_report.log              # 系统日志
└── exports/
    └── win_rate_report_*.json           # 导出的报表文件
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件是否存在
   - 确认数据库路径配置正确
   - 验证数据库表结构完整

2. **钉钉通知发送失败**
   - 检查钉钉Webhook URL是否正确
   - 确认网络连接正常
   - 验证钉钉机器人配置

3. **调度任务不执行**
   - 检查系统时区设置
   - 确认调度器配置正确
   - 查看系统日志获取详细错误信息

### 日志查看

```bash
# 查看实时日志
tail -f logs/win_rate_report.log

# 查看错误日志
grep "ERROR" logs/win_rate_report.log
```

## 📞 技术支持

如遇到问题，请：

1. 查看系统日志文件
2. 运行测试程序验证功能
3. 检查配置文件格式
4. 确认依赖包安装完整

---

*本系统基于现有的信号结算数据，确保交易系统正常运行并生成信号数据后再使用本报表系统。*
