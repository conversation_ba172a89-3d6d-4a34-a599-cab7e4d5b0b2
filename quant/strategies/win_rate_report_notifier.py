#!/usr/bin/env python3
"""
胜率报表钉钉通知器
扩展现有钉钉通知功能，专门用于发送胜率统计报表

Author: AI Assistant
Date: 2025-07-17
"""

import logging
from datetime import datetime
from typing import Dict, Optional, Tuple
from dataclasses import dataclass

from quant.utils.dingtalk import Dingtalk
from quant.config import config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class WinRateNotificationConfig:
    """胜率报表通知配置"""
    dingtalk_token: Optional[str] = None       # 钉钉Webhook Token
    enable_hourly_reports: bool = True         # 启用每小时报表
    enable_daily_summary: bool = True          # 启用每日总结
    enable_error_alerts: bool = True           # 启用错误警报
    
    # 报表发送条件
    min_signals_for_hourly: int = 1           # 每小时报表最小信号数
    min_signals_for_daily: int = 5            # 每日总结最小信号数
    
    # 格式化选项
    use_emoji: bool = True                     # 使用表情符号
    show_detailed_stats: bool = True           # 显示详细统计
    show_trend_analysis: bool = True           # 显示趋势分析


class WinRateReportNotifier:
    """胜率报表钉钉通知器"""
    
    def __init__(self, config_obj: Optional[WinRateNotificationConfig] = None):
        """
        初始化通知器
        
        Args:
            config_obj: 通知配置，如果为None则使用默认配置
        """
        self.config = config_obj or WinRateNotificationConfig()
        
        # 如果配置中没有指定钉钉token，则使用全局配置
        if not self.config.dingtalk_token:
            self.config.dingtalk_token = config.dingtalk
        
        # 统计信息
        self.sent_reports_count = 0
        self.last_report_time = None
        
        logger.info(f"胜率报表通知器已初始化，Token配置: {self.config.dingtalk_token is not None}")
    
    async def send_hourly_report(self, report: Dict) -> Tuple[bool, Optional[str]]:
        """
        发送每小时胜率报表
        
        Args:
            report: 胜率报表数据
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_hourly_reports:
            return True, None
        
        try:
            # 检查发送条件
            today_stats = report.get('summary', {}).get('today', {})
            if today_stats.get('total_signals', 0) < self.config.min_signals_for_hourly:
                logger.info("今日信号数不足，跳过每小时报表发送")
                return True, "信号数不足"
            
            # 构建报表消息
            message = self._build_hourly_report_message(report)
            
            # 发送通知
            success, error = self._send_dingtalk_message(message)
            
            if success:
                self._record_notification("hourly_report")
                logger.info("每小时胜率报表已发送")
            else:
                logger.error(f"每小时胜率报表发送失败: {error}")
            
            return success, error
            
        except Exception as e:
            logger.error(f"发送每小时报表异常: {e}")
            return False, str(e)
    
    async def send_daily_summary(self, report: Dict) -> Tuple[bool, Optional[str]]:
        """
        发送每日胜率总结
        
        Args:
            report: 胜率报表数据
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_daily_summary:
            return True, None
        
        try:
            # 检查发送条件
            today_stats = report.get('summary', {}).get('today', {})
            if today_stats.get('total_signals', 0) < self.config.min_signals_for_daily:
                logger.info("今日信号数不足，跳过每日总结发送")
                return True, "信号数不足"
            
            # 构建总结消息
            message = self._build_daily_summary_message(report)
            
            # 发送通知
            success, error = self._send_dingtalk_message(message)
            
            if success:
                self._record_notification("daily_summary")
                logger.info("每日胜率总结已发送")
            else:
                logger.error(f"每日胜率总结发送失败: {error}")
            
            return success, error
            
        except Exception as e:
            logger.error(f"发送每日总结异常: {e}")
            return False, str(e)
    
    async def send_error_alert(self, error_info: Dict) -> Tuple[bool, Optional[str]]:
        """
        发送错误警报
        
        Args:
            error_info: 错误信息
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_error_alerts:
            return True, None
        
        try:
            # 构建错误警报消息
            message = self._build_error_alert_message(error_info)
            
            # 发送通知
            success, error = self._send_dingtalk_message(message)
            
            if success:
                self._record_notification("error_alert")
                logger.info("错误警报已发送")
            else:
                logger.error(f"错误警报发送失败: {error}")
            
            return success, error
            
        except Exception as e:
            logger.error(f"发送错误警报异常: {e}")
            return False, str(e)
    
    def _build_hourly_report_message(self, report: Dict) -> str:
        """构建每小时报表消息"""
        try:
            current_time = datetime.now().strftime('%H:%M')
            today_stats = report.get('summary', {}).get('today', {})
            week_stats = report.get('summary', {}).get('week', {})
            
            # 基础信息
            emoji = "📊" if self.config.use_emoji else ""
            title = f"{emoji} 胜率统计报表 - {current_time}"
            
            # 今日统计
            today_signals = today_stats.get('total_signals', 0)
            today_settled = today_stats.get('settled_signals', 0)
            today_win_rate = today_stats.get('win_rate', 0)
            today_pnl = today_stats.get('total_pnl', 0)
            
            # 本周统计
            week_signals = week_stats.get('total_signals', 0)
            week_win_rate = week_stats.get('win_rate', 0)
            week_pnl = week_stats.get('total_pnl', 0)
            
            # 构建消息
            message = f"""### {title}

> **📅 统计时间**: {datetime.now().strftime('%Y-%m-%d %H:%M')}

#### 🎯 今日表现
> **信号总数**: {today_signals}
> **已结算**: {today_settled}
> **胜率**: {today_win_rate:.1f}%
> **总盈亏**: {today_pnl:+.2f}%

#### 📈 本周概览
> **信号总数**: {week_signals}
> **胜率**: {week_win_rate:.1f}%
> **总盈亏**: {week_pnl:+.2f}%"""
            
            # 添加详细统计
            if self.config.show_detailed_stats:
                detailed_stats = report.get('detailed_stats', {}).get('today', {})
                if detailed_stats:
                    up_win_rate = detailed_stats.get('up_win_rate', 0)
                    down_win_rate = detailed_stats.get('down_win_rate', 0)
                    strong_win_rate = detailed_stats.get('strong_win_rate', 0)
                    
                    message += f"""

#### 📋 详细分析
> **看涨胜率**: {up_win_rate:.1f}%
> **看跌胜率**: {down_win_rate:.1f}%
> **强信号胜率**: {strong_win_rate:.1f}%"""
            
            # 添加趋势分析
            if self.config.show_trend_analysis:
                trend = report.get('trend_analysis', {})
                if trend:
                    performance_trend = trend.get('performance_trend', '未知')
                    recommendation = trend.get('recommendation', '无建议')
                    
                    message += f"""

#### 🔍 趋势分析
> **表现趋势**: {performance_trend}
> **操作建议**: {recommendation}"""
            
            return message
            
        except Exception as e:
            logger.error(f"构建每小时报表消息失败: {e}")
            return f"### ❌ 报表生成失败\n\n> 错误信息: {str(e)}"
    
    def _build_daily_summary_message(self, report: Dict) -> str:
        """构建每日总结消息"""
        try:
            today_date = datetime.now().strftime('%Y-%m-%d')
            today_stats = report.get('summary', {}).get('today', {})
            week_stats = report.get('summary', {}).get('week', {})
            month_stats = report.get('summary', {}).get('month', {})
            
            # 基础信息
            emoji = "🌟" if self.config.use_emoji else ""
            title = f"{emoji} 每日胜率总结 - {today_date}"
            
            # 统计数据
            today_signals = today_stats.get('total_signals', 0)
            today_settled = today_stats.get('settled_signals', 0)
            today_win_rate = today_stats.get('win_rate', 0)
            today_pnl = today_stats.get('total_pnl', 0)
            
            week_win_rate = week_stats.get('win_rate', 0)
            month_win_rate = month_stats.get('win_rate', 0)
            
            # 构建消息
            message = f"""### {title}

> **📅 统计日期**: {today_date}
> **🕐 生成时间**: {datetime.now().strftime('%H:%M:%S')}

#### 🎯 今日完整统计
> **信号总数**: {today_signals}
> **已结算数**: {today_settled}
> **胜率**: {today_win_rate:.1f}%
> **总盈亏**: {today_pnl:+.2f}%

#### 📊 历史对比
> **本周胜率**: {week_win_rate:.1f}%
> **本月胜率**: {month_win_rate:.1f}%"""
            
            # 添加性能指标
            performance_metrics = report.get('performance_metrics', {})
            if performance_metrics:
                consistency = performance_metrics.get('consistency', 0)
                signal_quality = performance_metrics.get('signal_quality', '未知')
                
                message += f"""

#### 🏆 性能指标
> **一致性得分**: {consistency:.1f}/100
> **信号质量**: {signal_quality}"""
            
            # 添加趋势分析
            trend = report.get('trend_analysis', {})
            if trend:
                recent_performance = trend.get('recent_performance', '无数据')
                win_streak = trend.get('win_streak', 0)
                loss_streak = trend.get('loss_streak', 0)
                recommendation = trend.get('recommendation', '无建议')
                
                message += f"""

#### 🔍 趋势分析
> **最近表现**: {recent_performance}
> **最大连胜**: {win_streak}次
> **最大连败**: {loss_streak}次
> **操作建议**: {recommendation}"""
            
            # 添加总结
            if today_win_rate >= 70:
                summary_emoji = "🎉"
                summary_text = "今日表现优秀！"
            elif today_win_rate >= 55:
                summary_emoji = "👍"
                summary_text = "今日表现良好"
            elif today_win_rate >= 45:
                summary_emoji = "😐"
                summary_text = "今日表现平稳"
            else:
                summary_emoji = "⚠️"
                summary_text = "今日表现需要关注"
            
            message += f"""

#### {summary_emoji} 每日总结
> {summary_text}
> 
> 明日继续加油！💪"""
            
            return message
            
        except Exception as e:
            logger.error(f"构建每日总结消息失败: {e}")
            return f"### ❌ 每日总结生成失败\n\n> 错误信息: {str(e)}"
    
    def _build_error_alert_message(self, error_info: Dict) -> str:
        """构建错误警报消息"""
        try:
            error_message = error_info.get('message', '未知错误')
            timestamp = error_info.get('timestamp', datetime.now().isoformat())
            
            message = f"""### ⚠️ 胜率报表系统错误警报

> **🕐 发生时间**: {timestamp}
> **❌ 错误信息**: {error_message}

#### 🔧 处理建议
> 1. 检查数据库连接状态
> 2. 验证信号数据完整性
> 3. 查看系统日志获取详细信息
> 4. 如问题持续，请联系技术支持

---
*此为自动警报，请及时处理*"""
            
            return message
            
        except Exception as e:
            logger.error(f"构建错误警报消息失败: {e}")
            return f"### ❌ 错误警报生成失败\n\n> 错误信息: {str(e)}"
    
    def _send_dingtalk_message(self, message: str) -> Tuple[bool, Optional[str]]:
        """发送钉钉消息"""
        try:
            success, error = Dingtalk.markdown(
                content=message,
                token=self.config.dingtalk_token
            )
            return success is not None, error
        except Exception as e:
            logger.error(f"钉钉消息发送异常: {e}")
            return False, str(e)
    
    def _record_notification(self, notification_type: str):
        """记录通知发送"""
        self.sent_reports_count += 1
        self.last_report_time = datetime.now()
        logger.debug(f"已记录通知: {notification_type}")
    
    def get_notification_stats(self) -> Dict:
        """获取通知统计信息"""
        return {
            'sent_reports_count': self.sent_reports_count,
            'last_report_time': self.last_report_time.isoformat() if self.last_report_time else None,
            'config': self.config.__dict__
        }
