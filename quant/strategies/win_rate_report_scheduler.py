#!/usr/bin/env python3
"""
胜率报表定时调度器
使用APScheduler实现定时任务调度，支持每日数据重新计算和每小时报表发送

Author: AI Assistant
Date: 2025-07-17
"""

import asyncio
import logging
from datetime import datetime, time
from typing import Optional, Callable
from dataclasses import dataclass

try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    from apscheduler.triggers.cron import CronTrigger
    from apscheduler.triggers.interval import IntervalTrigger
    from apscheduler.executors.asyncio import AsyncIOExecutor
    from apscheduler.jobstores.memory import MemoryJobStore
except ImportError:
    print("请安装APScheduler: pip install apscheduler")
    raise

from .win_rate_report_system import WinRateReportSystem

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SchedulerConfig:
    """调度器配置"""
    enable_daily_calculation: bool = True      # 启用每日数据重新计算
    enable_hourly_reports: bool = True         # 启用每小时报表发送
    daily_calculation_time: str = "00:00"      # 每日计算时间 (HH:MM)
    hourly_report_interval: int = 1            # 每小时报表间隔（小时）
    timezone: str = "Asia/Shanghai"            # 时区
    max_workers: int = 3                       # 最大工作线程数
    
    # 报表发送配置
    send_empty_reports: bool = False           # 是否发送空报表
    min_signals_for_report: int = 1            # 发送报表的最小信号数
    
    # 错误处理配置
    max_retries: int = 3                       # 最大重试次数
    retry_interval: int = 300                  # 重试间隔（秒）


class WinRateReportScheduler:
    """胜率报表定时调度器"""
    
    def __init__(self, 
                 report_system: WinRateReportSystem,
                 config: Optional[SchedulerConfig] = None,
                 notification_callback: Optional[Callable] = None):
        """
        初始化调度器
        
        Args:
            report_system: 胜率报表系统实例
            config: 调度器配置
            notification_callback: 通知回调函数，用于发送报表
        """
        self.report_system = report_system
        self.config = config or SchedulerConfig()
        self.notification_callback = notification_callback
        
        # 初始化调度器
        self.scheduler = None
        self.is_running = False
        
        # 统计信息
        self.daily_calculation_count = 0
        self.hourly_report_count = 0
        self.error_count = 0
        self.last_daily_calculation = None
        self.last_hourly_report = None
        
        logger.info("胜率报表调度器初始化完成")
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        try:
            # 配置调度器
            jobstores = {
                'default': MemoryJobStore()
            }
            executors = {
                'default': AsyncIOExecutor()  # 移除max_workers参数，使用默认值
            }
            job_defaults = {
                'coalesce': True,
                'max_instances': 1,
                'misfire_grace_time': 300  # 5分钟容错时间
            }
            
            self.scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone=self.config.timezone
            )
            
            # 添加定时任务
            self._add_scheduled_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info(f"胜率报表调度器已启动，时区: {self.config.timezone}")
            
            # 立即执行一次报表生成（可选）
            if self.notification_callback:
                asyncio.create_task(self._generate_and_send_report())
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未在运行")
            return
        
        try:
            if self.scheduler:
                self.scheduler.shutdown(wait=True)
            self.is_running = False
            logger.info("胜率报表调度器已停止")
            
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    def _add_scheduled_jobs(self):
        """添加定时任务"""
        try:
            # 1. 每日数据重新计算任务
            if self.config.enable_daily_calculation:
                hour, minute = map(int, self.config.daily_calculation_time.split(':'))
                self.scheduler.add_job(
                    func=self._daily_calculation_job,
                    trigger=CronTrigger(hour=hour, minute=minute),
                    id='daily_calculation',
                    name='每日胜率数据重新计算',
                    replace_existing=True
                )
                logger.info(f"已添加每日计算任务: {self.config.daily_calculation_time}")
            
            # 2. 每小时报表发送任务
            if self.config.enable_hourly_reports:
                self.scheduler.add_job(
                    func=self._hourly_report_job,
                    trigger=IntervalTrigger(hours=self.config.hourly_report_interval),
                    id='hourly_reports',
                    name='每小时胜率报表发送',
                    replace_existing=True
                )
                logger.info(f"已添加每小时报表任务: 间隔{self.config.hourly_report_interval}小时")
            
            # 3. 系统健康检查任务（每30分钟）
            self.scheduler.add_job(
                func=self._health_check_job,
                trigger=IntervalTrigger(minutes=30),
                id='health_check',
                name='系统健康检查',
                replace_existing=True
            )
            logger.info("已添加系统健康检查任务")
            
        except Exception as e:
            logger.error(f"添加定时任务失败: {e}")
            raise
    
    async def _daily_calculation_job(self):
        """每日数据重新计算任务"""
        try:
            logger.info("🔄 开始执行每日胜率数据重新计算")
            
            # 生成综合报表（这会重新计算所有数据）
            report = self.report_system.generate_comprehensive_report()
            
            # 导出报表到文件
            timestamp = datetime.now().strftime('%Y%m%d')
            filename = f"./exports/daily_win_rate_report_{timestamp}.json"
            self.report_system.export_report_to_json(report, filename)
            
            # 更新统计
            self.daily_calculation_count += 1
            self.last_daily_calculation = datetime.now()
            
            logger.info(f"✅ 每日数据重新计算完成，报表已保存: {filename}")
            
            # 如果有通知回调，发送每日总结
            if self.notification_callback:
                await self.notification_callback(report, report_type="daily_summary")
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ 每日数据重新计算失败: {e}")
            
            # 发送错误通知
            if self.notification_callback:
                error_report = {
                    'error': True,
                    'message': f"每日数据计算失败: {str(e)}",
                    'timestamp': datetime.now().isoformat()
                }
                await self.notification_callback(error_report, report_type="error")
    
    async def _hourly_report_job(self):
        """每小时报表发送任务"""
        try:
            logger.info("📊 开始执行每小时胜率报表发送")
            
            # 生成当前报表
            await self._generate_and_send_report()
            
            # 更新统计
            self.hourly_report_count += 1
            self.last_hourly_report = datetime.now()
            
            logger.info("✅ 每小时报表发送完成")
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ 每小时报表发送失败: {e}")
    
    async def _generate_and_send_report(self):
        """生成并发送报表"""
        try:
            # 生成综合报表
            report = self.report_system.generate_comprehensive_report()
            
            # 检查是否满足发送条件
            if not self._should_send_report(report):
                logger.info("报表不满足发送条件，跳过发送")
                return
            
            # 发送报表通知
            if self.notification_callback:
                await self.notification_callback(report, report_type="hourly_report")
                logger.info("报表通知已发送")
            else:
                logger.warning("未配置通知回调函数，无法发送报表")
            
        except Exception as e:
            logger.error(f"生成并发送报表失败: {e}")
            raise
    
    def _should_send_report(self, report: dict) -> bool:
        """判断是否应该发送报表"""
        try:
            # 检查是否有错误
            if 'error' in report:
                return True  # 错误报表总是发送
            
            # 检查今日信号数量
            today_stats = report.get('summary', {}).get('today', {})
            total_signals = today_stats.get('total_signals', 0)
            
            if not self.config.send_empty_reports and total_signals < self.config.min_signals_for_report:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"判断报表发送条件失败: {e}")
            return True  # 出错时默认发送
    
    async def _health_check_job(self):
        """系统健康检查任务"""
        try:
            logger.debug("🔍 执行系统健康检查")
            
            # 检查数据库连接
            try:
                self.report_system._verify_database()
                db_status = "正常"
            except Exception as e:
                db_status = f"异常: {str(e)}"
            
            # 检查最近的信号数据
            recent_signals = self.report_system.get_recent_signals(5)
            signal_status = f"最近5个信号: {len(recent_signals)}个"
            
            # 记录健康状态
            health_info = {
                'timestamp': datetime.now().isoformat(),
                'database_status': db_status,
                'signal_status': signal_status,
                'scheduler_status': '运行中' if self.is_running else '已停止',
                'daily_calculation_count': self.daily_calculation_count,
                'hourly_report_count': self.hourly_report_count,
                'error_count': self.error_count
            }
            
            logger.debug(f"系统健康状态: {health_info}")
            
        except Exception as e:
            logger.error(f"系统健康检查失败: {e}")
    
    def get_scheduler_status(self) -> dict:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'daily_calculation_count': self.daily_calculation_count,
            'hourly_report_count': self.hourly_report_count,
            'error_count': self.error_count,
            'last_daily_calculation': self.last_daily_calculation.isoformat() if self.last_daily_calculation else None,
            'last_hourly_report': self.last_hourly_report.isoformat() if self.last_hourly_report else None,
            'config': self.config.__dict__
        }
    
    def force_daily_calculation(self):
        """强制执行每日计算"""
        if self.is_running and self.scheduler:
            self.scheduler.add_job(
                func=self._daily_calculation_job,
                trigger='date',
                run_date=datetime.now(),
                id='force_daily_calculation',
                replace_existing=True
            )
            logger.info("已触发强制每日计算")
        else:
            logger.warning("调度器未运行，无法执行强制计算")
    
    def force_hourly_report(self):
        """强制发送每小时报表"""
        if self.is_running and self.scheduler:
            self.scheduler.add_job(
                func=self._hourly_report_job,
                trigger='date',
                run_date=datetime.now(),
                id='force_hourly_report',
                replace_existing=True
            )
            logger.info("已触发强制报表发送")
        else:
            logger.warning("调度器未运行，无法发送报表")
