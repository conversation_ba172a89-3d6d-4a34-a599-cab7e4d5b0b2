#!/usr/bin/env python3
"""
胜率统计报表系统
自动化计算交易信号胜率统计，生成报表并发送到钉钉

Author: AI Assistant
Date: 2025-07-17
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class WinRateStats:
    """胜率统计数据结构"""
    period: str                    # 统计周期 (today, 7days, 30days)
    total_signals: int            # 总信号数
    total_settled: int            # 已结算数
    wins: int                     # 胜利次数
    losses: int                   # 失败次数
    ties: int                     # 平局次数
    win_rate: float              # 胜率百分比
    avg_confidence: float        # 平均置信度
    total_pnl: float            # 总盈亏
    avg_pnl: float              # 平均盈亏
    best_signal_pnl: float      # 最佳信号盈亏
    worst_signal_pnl: float     # 最差信号盈亏
    up_signals: int             # 看涨信号数
    down_signals: int           # 看跌信号数
    up_win_rate: float          # 看涨胜率
    down_win_rate: float        # 看跌胜率
    strong_signals: int         # 强信号数
    medium_signals: int         # 中等信号数
    weak_signals: int           # 弱信号数
    strong_win_rate: float      # 强信号胜率
    medium_win_rate: float      # 中等信号胜率
    weak_win_rate: float        # 弱信号胜率
    last_updated: str           # 最后更新时间


@dataclass
class TrendAnalysis:
    """趋势分析数据"""
    recent_performance: str      # 最近表现趋势
    win_streak: int             # 连胜次数
    loss_streak: int            # 连败次数
    performance_trend: str      # 表现趋势描述
    recommendation: str         # 建议


class WinRateReportSystem:
    """胜率统计报表系统"""
    
    def __init__(self, db_path: str = "./data/signal_settlement.db"):
        """
        初始化胜率报表系统
        
        Args:
            db_path: 信号结算数据库路径
        """
        self.db_path = db_path
        self.last_calculation_time = None
        
        # 验证数据库连接
        self._verify_database()
        logger.info(f"胜率报表系统初始化完成，数据库: {db_path}")
    
    def _verify_database(self):
        """验证数据库连接和表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查必要的表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('signal_records', 'settlement_stats')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            if 'signal_records' not in tables:
                raise Exception("signal_records表不存在")
            if 'settlement_stats' not in tables:
                raise Exception("settlement_stats表不存在")
                
            conn.close()
            logger.info("数据库验证通过")
            
        except Exception as e:
            logger.error(f"数据库验证失败: {e}")
            raise
    
    def calculate_win_rate_stats(self, period_days: int = 1) -> WinRateStats:
        """
        计算指定周期的胜率统计
        
        Args:
            period_days: 统计周期天数 (1=今日, 7=7天, 30=30天)
            
        Returns:
            WinRateStats: 胜率统计数据
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(days=period_days)
            
            # 获取基础统计数据
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_signals,
                    SUM(CASE WHEN result != 'PENDING' THEN 1 ELSE 0 END) as total_settled,
                    SUM(CASE WHEN result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    SUM(CASE WHEN result = 'LOSS' THEN 1 ELSE 0 END) as losses,
                    SUM(CASE WHEN result = 'TIE' THEN 1 ELSE 0 END) as ties,
                    AVG(confidence) as avg_confidence,
                    SUM(pnl) as total_pnl,
                    AVG(pnl) as avg_pnl,
                    MAX(pnl) as best_signal_pnl,
                    MIN(pnl) as worst_signal_pnl
                FROM signal_records 
                WHERE timestamp >= ? AND timestamp <= ?
            """, (start_time.isoformat(), end_time.isoformat()))
            
            basic_stats = cursor.fetchone()
            
            # 获取方向统计
            cursor.execute("""
                SELECT 
                    direction,
                    COUNT(*) as count,
                    SUM(CASE WHEN result = 'WIN' THEN 1 ELSE 0 END) as wins
                FROM signal_records 
                WHERE timestamp >= ? AND timestamp <= ? AND result != 'PENDING'
                GROUP BY direction
            """, (start_time.isoformat(), end_time.isoformat()))
            
            direction_stats = {row[0]: {'count': row[1], 'wins': row[2]} for row in cursor.fetchall()}
            
            # 获取强度统计
            cursor.execute("""
                SELECT 
                    signal_strength,
                    COUNT(*) as count,
                    SUM(CASE WHEN result = 'WIN' THEN 1 ELSE 0 END) as wins
                FROM signal_records 
                WHERE timestamp >= ? AND timestamp <= ? AND result != 'PENDING'
                GROUP BY signal_strength
            """, (start_time.isoformat(), end_time.isoformat()))
            
            strength_stats = {row[0]: {'count': row[1], 'wins': row[2]} for row in cursor.fetchall()}
            
            conn.close()
            
            # 处理统计数据
            total_signals = basic_stats[0] or 0
            total_settled = basic_stats[1] or 0
            wins = basic_stats[2] or 0
            losses = basic_stats[3] or 0
            ties = basic_stats[4] or 0
            
            # 计算胜率
            win_rate = (wins / (wins + losses) * 100) if (wins + losses) > 0 else 0
            
            # 方向统计
            up_stats = direction_stats.get('UP', {'count': 0, 'wins': 0})
            down_stats = direction_stats.get('DOWN', {'count': 0, 'wins': 0})
            up_win_rate = (up_stats['wins'] / up_stats['count'] * 100) if up_stats['count'] > 0 else 0
            down_win_rate = (down_stats['wins'] / down_stats['count'] * 100) if down_stats['count'] > 0 else 0
            
            # 强度统计
            strong_stats = strength_stats.get('STRONG', {'count': 0, 'wins': 0})
            medium_stats = strength_stats.get('MEDIUM', {'count': 0, 'wins': 0})
            weak_stats = strength_stats.get('WEAK', {'count': 0, 'wins': 0})
            
            strong_win_rate = (strong_stats['wins'] / strong_stats['count'] * 100) if strong_stats['count'] > 0 else 0
            medium_win_rate = (medium_stats['wins'] / medium_stats['count'] * 100) if medium_stats['count'] > 0 else 0
            weak_win_rate = (weak_stats['wins'] / weak_stats['count'] * 100) if weak_stats['count'] > 0 else 0
            
            # 确定周期名称
            period_name = "today" if period_days == 1 else f"{period_days}days"
            
            return WinRateStats(
                period=period_name,
                total_signals=total_signals,
                total_settled=total_settled,
                wins=wins,
                losses=losses,
                ties=ties,
                win_rate=round(win_rate, 2),
                avg_confidence=round(basic_stats[5] or 0, 2),
                total_pnl=round(basic_stats[6] or 0, 2),
                avg_pnl=round(basic_stats[7] or 0, 2),
                best_signal_pnl=round(basic_stats[8] or 0, 2),
                worst_signal_pnl=round(basic_stats[9] or 0, 2),
                up_signals=up_stats['count'],
                down_signals=down_stats['count'],
                up_win_rate=round(up_win_rate, 2),
                down_win_rate=round(down_win_rate, 2),
                strong_signals=strong_stats['count'],
                medium_signals=medium_stats['count'],
                weak_signals=weak_stats['count'],
                strong_win_rate=round(strong_win_rate, 2),
                medium_win_rate=round(medium_win_rate, 2),
                weak_win_rate=round(weak_win_rate, 2),
                last_updated=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"计算胜率统计失败: {e}")
            # 返回空统计数据
            return WinRateStats(
                period=f"{period_days}days",
                total_signals=0, total_settled=0, wins=0, losses=0, ties=0,
                win_rate=0, avg_confidence=0, total_pnl=0, avg_pnl=0,
                best_signal_pnl=0, worst_signal_pnl=0,
                up_signals=0, down_signals=0, up_win_rate=0, down_win_rate=0,
                strong_signals=0, medium_signals=0, weak_signals=0,
                strong_win_rate=0, medium_win_rate=0, weak_win_rate=0,
                last_updated=datetime.now().isoformat()
            )
    
    def analyze_trend(self, lookback_days: int = 7) -> TrendAnalysis:
        """
        分析最近的表现趋势
        
        Args:
            lookback_days: 回看天数
            
        Returns:
            TrendAnalysis: 趋势分析结果
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取最近的结算信号，按时间排序
            end_time = datetime.now()
            start_time = end_time - timedelta(days=lookback_days)
            
            cursor.execute("""
                SELECT result, settlement_time, pnl
                FROM signal_records 
                WHERE settlement_time >= ? AND settlement_time <= ? 
                AND result != 'PENDING'
                ORDER BY settlement_time DESC
                LIMIT 20
            """, (start_time.isoformat(), end_time.isoformat()))
            
            recent_signals = cursor.fetchall()
            conn.close()
            
            if not recent_signals:
                return TrendAnalysis(
                    recent_performance="无数据",
                    win_streak=0,
                    loss_streak=0,
                    performance_trend="数据不足",
                    recommendation="需要更多交易数据"
                )
            
            # 分析连胜连败
            win_streak = 0
            loss_streak = 0
            current_streak = 0
            last_result = None
            
            for result, _, _ in recent_signals:
                if result == 'WIN':
                    if last_result == 'WIN':
                        current_streak += 1
                    else:
                        current_streak = 1
                    win_streak = max(win_streak, current_streak)
                elif result == 'LOSS':
                    if last_result == 'LOSS':
                        current_streak += 1
                    else:
                        current_streak = 1
                    loss_streak = max(loss_streak, current_streak)
                else:  # TIE
                    current_streak = 0
                
                last_result = result
            
            # 分析表现趋势
            recent_wins = sum(1 for r, _, _ in recent_signals if r == 'WIN')
            recent_total = len(recent_signals)
            recent_win_rate = recent_wins / recent_total * 100
            
            if recent_win_rate >= 70:
                performance_trend = "表现优秀"
                recommendation = "继续保持当前策略"
            elif recent_win_rate >= 55:
                performance_trend = "表现良好"
                recommendation = "策略运行正常，可适当增加投注"
            elif recent_win_rate >= 45:
                performance_trend = "表现平稳"
                recommendation = "保持谨慎，观察市场变化"
            else:
                performance_trend = "表现不佳"
                recommendation = "建议降低投注或暂停交易"
            
            return TrendAnalysis(
                recent_performance=f"最近{recent_total}笔交易胜率{recent_win_rate:.1f}%",
                win_streak=win_streak,
                loss_streak=loss_streak,
                performance_trend=performance_trend,
                recommendation=recommendation
            )
            
        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            return TrendAnalysis(
                recent_performance="分析失败",
                win_streak=0,
                loss_streak=0,
                performance_trend="无法分析",
                recommendation="请检查数据"
            )

    def generate_comprehensive_report(self) -> Dict:
        """
        生成综合胜率报表

        Returns:
            Dict: 包含所有统计数据的综合报表
        """
        try:
            # 获取不同周期的统计数据
            today_stats = self.calculate_win_rate_stats(1)
            week_stats = self.calculate_win_rate_stats(7)
            month_stats = self.calculate_win_rate_stats(30)

            # 获取趋势分析
            trend_analysis = self.analyze_trend(7)

            # 生成报表
            report = {
                'report_time': datetime.now().isoformat(),
                'summary': {
                    'today': {
                        'total_signals': today_stats.total_signals,
                        'settled_signals': today_stats.total_settled,
                        'win_rate': today_stats.win_rate,
                        'total_pnl': today_stats.total_pnl
                    },
                    'week': {
                        'total_signals': week_stats.total_signals,
                        'settled_signals': week_stats.total_settled,
                        'win_rate': week_stats.win_rate,
                        'total_pnl': week_stats.total_pnl
                    },
                    'month': {
                        'total_signals': month_stats.total_signals,
                        'settled_signals': month_stats.total_settled,
                        'win_rate': month_stats.win_rate,
                        'total_pnl': month_stats.total_pnl
                    }
                },
                'detailed_stats': {
                    'today': today_stats.__dict__,
                    'week': week_stats.__dict__,
                    'month': month_stats.__dict__
                },
                'trend_analysis': trend_analysis.__dict__,
                'performance_metrics': {
                    'consistency': self._calculate_consistency_score(week_stats),
                    'risk_reward_ratio': self._calculate_risk_reward_ratio(week_stats),
                    'signal_quality': self._evaluate_signal_quality(week_stats)
                }
            }

            self.last_calculation_time = datetime.now()
            logger.info("综合报表生成完成")
            return report

        except Exception as e:
            logger.error(f"生成综合报表失败: {e}")
            return {
                'report_time': datetime.now().isoformat(),
                'error': str(e),
                'summary': {'today': {}, 'week': {}, 'month': {}},
                'detailed_stats': {},
                'trend_analysis': {},
                'performance_metrics': {}
            }

    def _calculate_consistency_score(self, stats: WinRateStats) -> float:
        """计算一致性得分"""
        try:
            if stats.total_settled < 5:
                return 0.0

            # 基于胜率稳定性和信号强度分布计算
            win_rate_score = min(stats.win_rate / 100, 1.0)

            # 信号强度分布得分
            total_signals = stats.strong_signals + stats.medium_signals + stats.weak_signals
            if total_signals > 0:
                strong_ratio = stats.strong_signals / total_signals
                distribution_score = strong_ratio * 0.6 + 0.4  # 偏向强信号
            else:
                distribution_score = 0.5

            consistency_score = (win_rate_score * 0.7 + distribution_score * 0.3) * 100
            return round(consistency_score, 2)

        except Exception:
            return 0.0

    def _calculate_risk_reward_ratio(self, stats: WinRateStats) -> float:
        """计算风险收益比"""
        try:
            if stats.losses == 0 or stats.avg_pnl <= 0:
                return 0.0

            # 简化的风险收益比计算
            avg_win = stats.total_pnl / stats.wins if stats.wins > 0 else 0
            avg_loss = abs(stats.worst_signal_pnl) if stats.worst_signal_pnl < 0 else 1

            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            return round(risk_reward, 2)

        except Exception:
            return 0.0

    def _evaluate_signal_quality(self, stats: WinRateStats) -> str:
        """评估信号质量"""
        try:
            if stats.total_settled < 5:
                return "数据不足"

            # 综合评估信号质量
            win_rate_score = stats.win_rate
            confidence_score = stats.avg_confidence

            if win_rate_score >= 70 and confidence_score >= 80:
                return "优秀"
            elif win_rate_score >= 60 and confidence_score >= 70:
                return "良好"
            elif win_rate_score >= 50 and confidence_score >= 60:
                return "一般"
            else:
                return "需要改进"

        except Exception:
            return "无法评估"

    def export_report_to_json(self, report: Dict, filename: str = None) -> str:
        """
        导出报表到JSON文件

        Args:
            report: 报表数据
            filename: 文件名，如果为None则自动生成

        Returns:
            str: 导出的文件路径
        """
        try:
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"./exports/win_rate_report_{timestamp}.json"

            # 确保导出目录存在
            import os
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            logger.info(f"报表已导出到: {filename}")
            return filename

        except Exception as e:
            logger.error(f"导出报表失败: {e}")
            return ""

    def get_recent_signals(self, limit: int = 10) -> List[Dict]:
        """
        获取最近的信号记录

        Args:
            limit: 返回记录数量限制

        Returns:
            List[Dict]: 最近的信号记录列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT signal_id, timestamp, direction, confidence, signal_price,
                       result, settlement_price, settlement_time, pnl, signal_strength
                FROM signal_records
                ORDER BY timestamp DESC
                LIMIT ?
            """, (limit,))

            columns = ['signal_id', 'timestamp', 'direction', 'confidence', 'signal_price',
                      'result', 'settlement_price', 'settlement_time', 'pnl', 'signal_strength']

            signals = []
            for row in cursor.fetchall():
                signal = dict(zip(columns, row))
                signals.append(signal)

            conn.close()
            return signals

        except Exception as e:
            logger.error(f"获取最近信号失败: {e}")
            return []
