"""
币安事件合约钉钉通知集成模块
发送交易信号、结算通知、风险提醒到钉钉群组
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import logging

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from quant.utils.dingtalk import Dingtalk
from quant.config import config
from .event_contract_decision_engine import TradingDecision, RiskLevel
from .event_contract_signal_generator_simple import SignalResult


@dataclass
class NotificationConfig:
    """通知配置"""
    enable_signal_notification: bool = True      # 启用交易信号通知
    enable_settlement_notification: bool = True  # 启用结算通知
    enable_risk_notification: bool = True        # 启用风险提醒
    enable_daily_summary: bool = True            # 启用日报
    
    # 钉钉Webhook URL (如果为None，则使用全局配置)
    dingtalk_token: Optional[str] = None
    
    # 🔧 修复：放宽通知频率限制
    min_signal_interval: int = 60   # 从300秒减少到60秒(1分钟)
    max_daily_notifications: int = 200  # 从50增加到200
    
    # 风险提醒阈值
    risk_notification_threshold: float = 0.5  # 损失比例阈值
    consecutive_loss_threshold: int = 3       # 连续亏损阈值


class EventContractDingtalkNotifier:
    """事件合约钉钉通知器"""
    
    def __init__(self, config_obj: Optional[NotificationConfig] = None):
        """
        初始化通知器
        
        Args:
            config_obj: 通知配置，如果为None则使用默认配置
        """
        self.config = config_obj or NotificationConfig()
        self.last_notification_time = None
        self.daily_notification_count = 0
        self.notification_history = []
        self.sent_settlements: set[str] = set()  # 防止重复结算通知
        
        # 如果配置中没有指定钉钉token，则使用全局配置
        if not self.config.dingtalk_token:
            self.config.dingtalk_token = config.dingtalk
        
        logger.info(f"事件合约钉钉通知器已初始化，Token: {self.config.dingtalk_token is not None}")
    
    def send_trading_signal(self, 
                          decision: TradingDecision,
                          signal_result: SignalResult,
                          market_data: Optional[Dict] = None) -> Tuple[bool, Optional[str]]:
        """
        发送交易信号通知（包含关键词：交易、小火箭）
        
        Args:
            decision: 交易决策结果
            signal_result: 信号生成结果
            market_data: 市场数据
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_signal_notification:
            return True, None
        
        # 检查通知频率限制
        if not self._check_notification_limit():
            return False, "通知频率超限"
        
        # 构建消息内容
        message = self._build_signal_message(decision, signal_result, market_data)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("trading_signal", decision.direction)
            logger.info(f"交易信号通知已发送: {decision.direction}")
        else:
            logger.error(f"交易信号通知发送失败: {error}")
        
        return success, error
    
    def send_settlement_notification(self, 
                                   order_id: str,
                                   result: str,
                                   pnl: float,
                                   decision: TradingDecision) -> Tuple[bool, Optional[str]]:
        """
        发送结算通知（同一 order_id 只发送一次）
        """
        # 去重：已发过则直接返回成功
        if order_id in self.sent_settlements:
            return True, None

        if not self.config.enable_settlement_notification:
            return True, None
        
        # 构建结算消息
        message = self._build_settlement_message(order_id, result, pnl, decision)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self.sent_settlements.add(order_id)
            self._record_notification("settlement", result)
            logger.info(f"结算通知已发送: {order_id} - {result}")
        else:
            logger.error(f"结算通知发送失败: {error}")
        
        return success, error
    
    def send_risk_alert(self, 
                       risk_type: str,
                       message: str,
                       current_loss: float,
                       loss_ratio: float) -> Tuple[bool, Optional[str]]:
        """
        发送风险提醒
        
        Args:
            risk_type: 风险类型
            message: 风险消息
            current_loss: 当前损失
            loss_ratio: 损失比例
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_risk_notification:
            return True, None
        
        # 构建风险提醒消息
        alert_message = self._build_risk_alert_message(risk_type, message, current_loss, loss_ratio)
        
        # 发送通知
        success, error = self._send_dingtalk_message(alert_message)
        
        if success:
            self._record_notification("risk_alert", risk_type)
            logger.info(f"风险提醒已发送: {risk_type}")
        else:
            logger.error(f"风险提醒发送失败: {error}")
        
        return success, error
    
    def send_daily_summary(self, 
                         stats: Dict,
                         risk_summary: Dict) -> Tuple[bool, Optional[str]]:
        """
        发送每日交易总结
        
        Args:
            stats: 交易统计数据
            risk_summary: 风险摘要
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_daily_summary:
            return True, None
        
        # 构建日报消息
        message = self._build_daily_summary_message(stats, risk_summary)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("daily_summary", "report")
            logger.info("每日交易总结已发送")
        else:
            logger.error(f"每日交易总结发送失败: {error}")
        
        return success, error

    # === 新增: 发送推荐 ===
    def send_recommendation(self, recommendation: Dict) -> Tuple[bool, Optional[str]]:
        """发送最终交易推荐（无需 TradingDecision）。"""

        if not self.config.enable_signal_notification:
            return True, None

        # 频率检查
        if not self._check_notification_limit():
            return False, "通知频率超限"

        message = self._build_recommendation_message(recommendation)
        success, error = self._send_dingtalk_message(message)
        if success:
            self._record_notification("recommendation", recommendation.get("direction", ""))
        return success, error

    def send_pending_signal(self, signal_result: SignalResult, market_data: Optional[Dict] = None) -> Tuple[bool, Optional[str]]:
        """
        发送pending信号提醒（第一阶段）
        
        Args:
            signal_result: 信号生成结果
            market_data: 市场数据
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_signal_notification:
            return True, None
        
        # 检查通知频率限制
        if not self._check_notification_limit():
            return False, "通知频率超限"
        
        # 构建pending信号消息
        message = self._build_pending_signal_message(signal_result, market_data)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("pending_signal", signal_result.direction)
            logger.info(f"Pending信号通知已发送: {signal_result.direction}")
        else:
            logger.error(f"Pending信号通知发送失败: {error}")
        
        return success, error

    def send_message(self, message: str) -> Tuple[bool, Optional[str]]:
        """发送通用文本消息（同步调用）"""
        return self._send_dingtalk_message(message)

    # ------------------------------------------------------------------
    # 内部工具方法
    # ------------------------------------------------------------------

    def _check_notification_limit(self) -> bool:
        """检查是否超过通知频率/数量限制"""
        now = datetime.now()
        
        # 🔧 修复：每日重置通知计数器
        current_date = now.date()
        if not hasattr(self, '_last_reset_date') or self._last_reset_date != current_date:
            self.daily_notification_count = 0
            self._last_reset_date = current_date
            logger.info(f"每日通知计数器已重置到新日期: {current_date}")

        # 每日通知数量上限
        if self.daily_notification_count >= self.config.max_daily_notifications:
            logger.warning(f"已达到每日通知上限: {self.daily_notification_count}/{self.config.max_daily_notifications}")
            return False

        # 最小时间间隔
        if self.last_notification_time is not None:
            time_diff = (now - self.last_notification_time).total_seconds()
            if time_diff < self.config.min_signal_interval:
                logger.debug(f"通知间隔过短: {time_diff}s < {self.config.min_signal_interval}s")
                return False

        return True

    def _record_notification(self, n_type: str, content: str) -> None:
        """记录一次已发送的通知"""
        self.last_notification_time = datetime.now()
        self.daily_notification_count += 1
        self.notification_history.append({
            "timestamp": self.last_notification_time,
            "type": n_type,
            "content": content,
        })

        # 只保留最近 100 条
        if len(self.notification_history) > 100:
            self.notification_history = self.notification_history[-100:]

    def _send_dingtalk_message(self, message: str) -> Tuple[bool, Optional[str]]:
        """实际调用钉钉 Markdown 接口发送消息"""
        try:
            token = self.config.dingtalk_token or config.dingtalk
            success, error = Dingtalk.markdown(content=message, token=token)
            return bool(success), error
        except Exception as e:
            logger.error(f"钉钉消息发送异常: {e}")
            return False, str(e)

    def get_notification_stats(self) -> Dict:
        """返回当日已发送通知统计信息"""
        today = datetime.now().date()
        today_history = [n for n in self.notification_history if n['timestamp'].date() == today]
        return {
            "sent_today": len(today_history),
            "remaining_quota": self.config.max_daily_notifications - self.daily_notification_count,
            "last_time": self.last_notification_time,
            "detail_breakdown": {
                "trading_signal": len([n for n in today_history if n['type'] == 'trading_signal']),
                "pending_signal": len([n for n in today_history if n['type'] == 'pending_signal']),
                "recommendation": len([n for n in today_history if n['type'] == 'recommendation']),
                "settlement": len([n for n in today_history if n['type'] == 'settlement']),
                "risk_alert": len([n for n in today_history if n['type'] == 'risk_alert']),
                "daily_summary": len([n for n in today_history if n['type'] == 'daily_summary']),
                "win_rate_report": len([n for n in today_history if n['type'] == 'win_rate_report'])
            }
        }

    def send_win_rate_report(self, report_data: Dict, report_type: str = "hourly") -> Tuple[bool, Optional[str]]:
        """
        发送胜率统计报表

        Args:
            report_data: 报表数据
            report_type: 报表类型 ("hourly", "daily", "weekly")

        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_daily_summary:  # 复用每日总结配置
            return True, None

        # 检查通知频率限制
        if not self._check_notification_limit():
            return False, "通知频率超限"

        # 构建报表消息
        message = self._build_win_rate_report_message(report_data, report_type)

        # 发送通知
        success, error = self._send_dingtalk_message(message)

        if success:
            self._record_notification("win_rate_report", report_type)
            logger.info(f"胜率报表已发送: {report_type}")
        else:
            logger.error(f"胜率报表发送失败: {error}")

        return success, error

    def _build_recommendation_message(self, rec: Dict) -> str:
        """构造最终交易推荐消息"""
        direction_emoji = "🚀" if rec.get("direction") == "UP" else "📉"
        stake       = rec.get("stake", 0)
        confidence  = rec.get("confidence", 0)
        score       = rec.get("score", 0)
        remaining   = rec.get("remaining_time", "-")

        signal_result = rec.get("signal_result")
        signal_id    = getattr(signal_result, "signal_id", None) or rec.get("signal_id", "未找到")
        signal_price = getattr(signal_result, "signal_price", 0.0) or rec.get("signal_price", 0.0)

        msg  = f"🎯 **事件合约交易推荐** {direction_emoji}\n\n"
        msg += f"🆔 信号ID: {signal_id}\n"
        msg += f"💰 信号价格: {signal_price:.2f} USDT\n\n"

        msg += f"📈 方向: **{rec.get('direction')}**\n"
        msg += f"💵 建议投入: **{stake} USDT**\n"
        msg += f"🎯 置信度: **{confidence:.1f}%**\n"
        msg += f"📊 因子得分: **{score:.1f}/100**\n"
        msg += f"⏰ 剩余时间: {remaining}s\n"
        msg += f"🕒 生成时间: {rec.get('generated_at')}\n\n"

        msg += "💡 该推荐仅供参考，实际操作请自行评估风控。"
        return msg

    def _build_win_rate_report_message(self, report_data: Dict, report_type: str) -> str:
        """构建胜率报表消息"""
        try:
            current_time = datetime.now().strftime('%H:%M')

            # 根据报表类型选择标题和表情
            if report_type == "daily":
                title = f"🌟 每日胜率总结 - {datetime.now().strftime('%Y-%m-%d')}"
            elif report_type == "weekly":
                title = f"📊 每周胜率报表 - {current_time}"
            else:  # hourly
                title = f"📊 胜率统计报表 - {current_time}"

            # 获取统计数据
            summary = report_data.get('summary', {})
            today_stats = summary.get('today', {})
            week_stats = summary.get('week', {})

            # 基础统计
            today_signals = today_stats.get('total_signals', 0)
            today_settled = today_stats.get('settled_signals', 0)
            today_win_rate = today_stats.get('win_rate', 0)
            today_pnl = today_stats.get('total_pnl', 0)

            week_signals = week_stats.get('total_signals', 0)
            week_win_rate = week_stats.get('win_rate', 0)
            week_pnl = week_stats.get('total_pnl', 0)

            # 构建消息
            message = f"### {title}\n\n"
            message += f"> **📅 统计时间**: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n"

            # 今日表现
            message += f"#### 🎯 今日表现\n"
            message += f"> **信号总数**: {today_signals}\n"
            message += f"> **已结算**: {today_settled}\n"
            message += f"> **胜率**: {today_win_rate:.1f}%\n"
            message += f"> **总盈亏**: {today_pnl:+.2f}%\n\n"

            # 本周概览
            if week_signals > 0:
                message += f"#### 📈 本周概览\n"
                message += f"> **信号总数**: {week_signals}\n"
                message += f"> **胜率**: {week_win_rate:.1f}%\n"
                message += f"> **总盈亏**: {week_pnl:+.2f}%\n\n"

            # 详细分析（如果是每日报表）
            if report_type == "daily":
                detailed_stats = report_data.get('detailed_stats', {}).get('today', {})
                if detailed_stats:
                    up_win_rate = detailed_stats.get('up_win_rate', 0)
                    down_win_rate = detailed_stats.get('down_win_rate', 0)
                    strong_win_rate = detailed_stats.get('strong_win_rate', 0)

                    message += f"#### 📋 详细分析\n"
                    message += f"> **看涨胜率**: {up_win_rate:.1f}%\n"
                    message += f"> **看跌胜率**: {down_win_rate:.1f}%\n"
                    message += f"> **强信号胜率**: {strong_win_rate:.1f}%\n\n"

                # 趋势分析
                trend = report_data.get('trend_analysis', {})
                if trend:
                    performance_trend = trend.get('performance_trend', '未知')
                    recommendation = trend.get('recommendation', '无建议')
                    win_streak = trend.get('win_streak', 0)
                    loss_streak = trend.get('loss_streak', 0)

                    message += f"#### 🔍 趋势分析\n"
                    message += f"> **表现趋势**: {performance_trend}\n"
                    message += f"> **最大连胜**: {win_streak}次\n"
                    message += f"> **最大连败**: {loss_streak}次\n"
                    message += f"> **操作建议**: {recommendation}\n\n"

                # 每日总结
                if today_win_rate >= 70:
                    summary_emoji = "🎉"
                    summary_text = "今日表现优秀！"
                elif today_win_rate >= 55:
                    summary_emoji = "👍"
                    summary_text = "今日表现良好"
                elif today_win_rate >= 45:
                    summary_emoji = "😐"
                    summary_text = "今日表现平稳"
                else:
                    summary_emoji = "⚠️"
                    summary_text = "今日表现需要关注"

                message += f"#### {summary_emoji} 每日总结\n"
                message += f"> {summary_text}\n"
                message += f"> \n"
                message += f"> 明日继续加油！💪"

            return message

        except Exception as e:
            logger.error(f"构建胜率报表消息失败: {e}")
            return f"### ❌ 胜率报表生成失败\n\n> 错误信息: {str(e)}"

    def _build_pending_signal_message(self, signal_result: SignalResult, market_data: Optional[Dict] = None) -> str:
        """构建 pending 信号消息（第一阶段）"""
        # 选择方向图标
        direction_icon = "🚀" if signal_result.direction == "UP" else "📉"

        # 提取市场数据安全处理
        kline_sequence = market_data.get('kline_sequence', 0) if market_data else 0
        signal_count   = market_data.get('signal_count', 0) if market_data else 0
        kline_time     = market_data.get('kline_time', '未知') if market_data else '未知'
        signal_id      = market_data.get('signal_id', '未分配') if market_data else '未分配'
        signal_price   = market_data.get('signal_price', 0.0) if market_data else 0.0

        # 开始拼装消息
        msg  = f"🔔 **潜在信号检测** {direction_icon}\n\n"
        msg += f"🆔 信号ID: {signal_id}\n"
        msg += f"💰 信号价格: {signal_price:.2f} USDT\n\n"

        msg += f"📊 K线序号: 第{kline_sequence}根15分钟K线 ({kline_time})\n"
        msg += f"🔢 信号序号: 今日第{signal_count}个信号\n\n"

        msg += f"📈 交易方向: {signal_result.direction} {direction_icon}\n"
        msg += f"🎯 置信度: {signal_result.confidence:.1f}%\n"
        msg += f"📊 技术分: {signal_result.technical_score:.1f}分\n"
        msg += f"⚠️ 风险等级: {signal_result.risk_level}\n\n"

        # 市场条件
        market_status = getattr(signal_result, 'market_status', '')
        if market_status:
            msg += f"🌟 市场条件: {market_status}\n"
        else:
            if signal_result.confidence >= 70:
                inferred = "强势信号"
            elif signal_result.confidence >= 60:
                inferred = "中等信号"
            elif signal_result.confidence >= 50:
                inferred = "弱势信号"
            else:
                inferred = "信号待确认"
            msg += f"🌟 市场条件: {inferred}\n"

        # 决策原因
        msg += f"💡 决策原因: 满足交易条件, 信心度{signal_result.confidence:.1f}%\n\n"

        # 用户提醒
        if getattr(signal_result, 'user_reminder', ''):
            msg += f"🔍 市场提醒: {signal_result.user_reminder}\n\n"

        # 时间戳与进度
        msg += f"⏰ 信号时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        msg += f"📈 今日进度: {kline_sequence}/96 (15分钟K线)\n\n"

        # 等待及跟踪
        msg += "⏳ 等待入场时机评估... 系统将在接下来的15分钟内寻找最佳入场点\n\n"
        msg += f"🏷️ 跟踪提醒: 请记住信号ID [{signal_id}], 用于结算通知对应\n\n"

        # 结束祝福
        msg += f"🎉 祝**交易**顺利！{direction_icon}小火箭{direction_icon}起飞！"
        return msg
