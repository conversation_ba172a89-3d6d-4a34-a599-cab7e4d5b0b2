#!/usr/bin/env python3
"""
胜率统计报表系统主程序
自动化胜率统计、报表生成和钉钉通知的完整系统

Author: AI Assistant
Date: 2025-07-17
"""

import asyncio
import json
import logging
import signal
import sys
import os
from datetime import datetime
from typing import Dict, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from quant.strategies.win_rate_report_system import WinRateReportSystem
from quant.strategies.win_rate_report_scheduler import WinRateReportScheduler, SchedulerConfig
from quant.strategies.win_rate_report_notifier import WinRateReportNotifier, WinRateNotificationConfig

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/win_rate_report.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class WinRateReportApp:
    """胜率报表应用主类"""
    
    def __init__(self, config_file: str = "./config/win_rate_report_config.json"):
        """
        初始化应用
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        
        # 初始化组件
        self.report_system = None
        self.scheduler = None
        self.notifier = None
        
        # 运行状态
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        logger.info("胜率报表应用初始化完成")
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                # 创建默认配置
                config = self._create_default_config()
                self._save_config(config)
                logger.info(f"创建默认配置文件: {self.config_file}")
            
            return config
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict:
        """创建默认配置"""
        return {
            "database": {
                "path": "./data/signal_settlement.db"
            },
            "scheduler": {
                "enable_daily_calculation": True,
                "enable_hourly_reports": True,
                "daily_calculation_time": "00:00",
                "hourly_report_interval": 1,
                "timezone": "Asia/Shanghai",
                "max_workers": 3,
                "send_empty_reports": False,
                "min_signals_for_report": 1,
                "max_retries": 3,
                "retry_interval": 300
            },
            "notification": {
                "dingtalk_token": None,  # 需要用户配置
                "enable_hourly_reports": True,
                "enable_daily_summary": True,
                "enable_error_alerts": True,
                "min_signals_for_hourly": 1,
                "min_signals_for_daily": 5,
                "use_emoji": True,
                "show_detailed_stats": True,
                "show_trend_analysis": True
            },
            "logging": {
                "level": "INFO",
                "file": "./logs/win_rate_report.log"
            }
        }
    
    def _save_config(self, config: Dict):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"配置文件已保存: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def _initialize_components(self):
        """初始化系统组件"""
        try:
            # 1. 初始化报表系统
            db_path = self.config['database']['path']
            self.report_system = WinRateReportSystem(db_path)
            logger.info("胜率报表系统初始化完成")
            
            # 2. 初始化通知器
            notification_config = WinRateNotificationConfig(**self.config['notification'])
            self.notifier = WinRateReportNotifier(notification_config)
            logger.info("通知器初始化完成")
            
            # 3. 初始化调度器
            scheduler_config = SchedulerConfig(**self.config['scheduler'])
            self.scheduler = WinRateReportScheduler(
                report_system=self.report_system,
                config=scheduler_config,
                notification_callback=self._notification_callback
            )
            logger.info("调度器初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise
    
    async def _notification_callback(self, report: Dict, report_type: str = "hourly_report"):
        """通知回调函数"""
        try:
            if report_type == "hourly_report":
                await self.notifier.send_hourly_report(report)
            elif report_type == "daily_summary":
                await self.notifier.send_daily_summary(report)
            elif report_type == "error":
                await self.notifier.send_error_alert(report)
            else:
                logger.warning(f"未知的报表类型: {report_type}")
                
        except Exception as e:
            logger.error(f"通知回调执行失败: {e}")
    
    async def start(self):
        """启动应用"""
        if self.is_running:
            logger.warning("应用已在运行中")
            return
        
        try:
            logger.info("🚀 启动胜率报表系统...")
            
            # 初始化组件
            self._initialize_components()
            
            # 启动调度器
            self.scheduler.start()
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            self.is_running = True
            logger.info("✅ 胜率报表系统启动成功")
            
            # 发送启动通知
            startup_report = {
                'message': '胜率报表系统已启动',
                'timestamp': datetime.now().isoformat(),
                'config_summary': {
                    'database': self.config['database']['path'],
                    'hourly_reports': self.config['scheduler']['enable_hourly_reports'],
                    'daily_calculation': self.config['scheduler']['enable_daily_calculation']
                }
            }
            await self.notifier.send_error_alert(startup_report)
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            raise
    
    async def stop(self):
        """停止应用"""
        if not self.is_running:
            logger.warning("应用未在运行")
            return
        
        try:
            logger.info("🛑 停止胜率报表系统...")
            
            # 停止调度器
            if self.scheduler:
                self.scheduler.stop()
            
            # 发送停止通知
            if self.notifier:
                shutdown_report = {
                    'message': '胜率报表系统已停止',
                    'timestamp': datetime.now().isoformat()
                }
                await self.notifier.send_error_alert(shutdown_report)
            
            self.is_running = False
            self.shutdown_event.set()
            
            logger.info("✅ 胜率报表系统已停止")
            
        except Exception as e:
            logger.error(f"停止应用失败: {e}")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"接收到信号 {signum}，准备关闭...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'is_running': self.is_running,
            'start_time': datetime.now().isoformat(),
            'config_file': self.config_file
        }
        
        if self.scheduler:
            status['scheduler'] = self.scheduler.get_scheduler_status()
        
        if self.notifier:
            status['notifier'] = self.notifier.get_notification_stats()
        
        return status
    
    def force_daily_calculation(self):
        """强制执行每日计算"""
        if self.scheduler:
            self.scheduler.force_daily_calculation()
        else:
            logger.warning("调度器未初始化，无法执行强制计算")
    
    def force_hourly_report(self):
        """强制发送每小时报表"""
        if self.scheduler:
            self.scheduler.force_hourly_report()
        else:
            logger.warning("调度器未初始化，无法发送报表")


async def main():
    """主函数"""
    try:
        # 确保必要的目录存在
        os.makedirs('./logs', exist_ok=True)
        os.makedirs('./config', exist_ok=True)
        os.makedirs('./data', exist_ok=True)
        os.makedirs('./exports', exist_ok=True)
        
        # 创建应用实例
        app = WinRateReportApp()
        
        # 启动应用
        await app.start()
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭...")
    except Exception as e:
        logger.error(f"应用运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 检查依赖
    try:
        import apscheduler
    except ImportError:
        print("错误: 请安装APScheduler: pip install apscheduler")
        sys.exit(1)
    
    # 运行应用
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)
