#!/usr/bin/env python3
"""
胜率统计报表系统演示程序
展示如何使用胜率报表系统的各项功能

Author: AI Assistant
Date: 2025-07-17
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.win_rate_report_system import WinRateReportSystem
from quant.strategies.win_rate_report_scheduler import WinRateReportScheduler, SchedulerConfig
from quant.strategies.win_rate_report_notifier import WinRateReportNotifier, WinRateNotificationConfig


def demo_basic_usage():
    """演示基本使用方法"""
    print("🚀 胜率统计报表系统演示")
    print("=" * 60)
    
    # 1. 初始化报表系统
    print("\n1️⃣ 初始化报表系统")
    report_system = WinRateReportSystem("./data/signal_settlement.db")
    print("   ✅ 报表系统初始化完成")
    
    # 2. 计算不同周期的胜率统计
    print("\n2️⃣ 计算胜率统计")
    
    # 今日统计
    today_stats = report_system.calculate_win_rate_stats(1)
    print(f"   📊 今日统计:")
    print(f"      总信号数: {today_stats.total_signals}")
    print(f"      已结算数: {today_stats.total_settled}")
    print(f"      胜率: {today_stats.win_rate}%")
    print(f"      总盈亏: {today_stats.total_pnl:+.2f}%")
    print(f"      平均置信度: {today_stats.avg_confidence:.1f}%")
    
    # 本周统计
    week_stats = report_system.calculate_win_rate_stats(7)
    print(f"   📈 本周统计:")
    print(f"      总信号数: {week_stats.total_signals}")
    print(f"      胜率: {week_stats.win_rate}%")
    print(f"      看涨胜率: {week_stats.up_win_rate}%")
    print(f"      看跌胜率: {week_stats.down_win_rate}%")
    
    # 本月统计
    month_stats = report_system.calculate_win_rate_stats(30)
    print(f"   📅 本月统计:")
    print(f"      总信号数: {month_stats.total_signals}")
    print(f"      胜率: {month_stats.win_rate}%")
    print(f"      强信号胜率: {month_stats.strong_win_rate}%")
    
    # 3. 趋势分析
    print("\n3️⃣ 趋势分析")
    trend = report_system.analyze_trend(7)
    print(f"   🔍 最近表现: {trend.recent_performance}")
    print(f"   📊 表现趋势: {trend.performance_trend}")
    print(f"   💡 操作建议: {trend.recommendation}")
    print(f"   🔥 最大连胜: {trend.win_streak}次")
    print(f"   ❄️ 最大连败: {trend.loss_streak}次")
    
    # 4. 生成综合报表
    print("\n4️⃣ 生成综合报表")
    report = report_system.generate_comprehensive_report()
    print(f"   📋 报表生成时间: {report['report_time']}")
    print(f"   🎯 一致性得分: {report['performance_metrics']['consistency']:.1f}/100")
    print(f"   ⭐ 信号质量: {report['performance_metrics']['signal_quality']}")
    
    # 5. 导出报表
    print("\n5️⃣ 导出报表")
    export_file = report_system.export_report_to_json(report)
    if export_file:
        print(f"   💾 报表已导出: {export_file}")
    
    # 6. 获取最近信号
    print("\n6️⃣ 最近信号记录")
    recent_signals = report_system.get_recent_signals(5)
    print(f"   📝 最近 {len(recent_signals)} 个信号:")
    for i, signal in enumerate(recent_signals, 1):
        status_emoji = {"WIN": "✅", "LOSS": "❌", "TIE": "⚖️", "PENDING": "⏳"}.get(signal['result'], "❓")
        print(f"      {i}. {signal['signal_id']}: {signal['direction']} {status_emoji} ({signal['confidence']:.1f}%)")


def demo_notification_messages():
    """演示通知消息格式"""
    print("\n" + "=" * 60)
    print("📱 通知消息格式演示")
    print("=" * 60)
    
    # 初始化通知器
    config = WinRateNotificationConfig(
        dingtalk_token="demo_token",
        use_emoji=True,
        show_detailed_stats=True,
        show_trend_analysis=True
    )
    notifier = WinRateReportNotifier(config)
    
    # 创建示例报表数据
    sample_report = {
        'report_time': datetime.now().isoformat(),
        'summary': {
            'today': {
                'total_signals': 12,
                'settled_signals': 10,
                'win_rate': 70.0,
                'total_pnl': 2.35
            },
            'week': {
                'total_signals': 45,
                'settled_signals': 42,
                'win_rate': 64.3,
                'total_pnl': 8.92
            },
            'month': {
                'total_signals': 180,
                'settled_signals': 175,
                'win_rate': 61.7,
                'total_pnl': 28.45
            }
        },
        'detailed_stats': {
            'today': {
                'up_win_rate': 75.0,
                'down_win_rate': 65.0,
                'strong_win_rate': 85.0
            }
        },
        'trend_analysis': {
            'recent_performance': '最近15笔交易胜率73.3%',
            'performance_trend': '表现优秀',
            'recommendation': '继续保持当前策略',
            'win_streak': 4,
            'loss_streak': 2
        },
        'performance_metrics': {
            'consistency': 82.5,
            'signal_quality': '优秀'
        }
    }
    
    # 演示每小时报表消息
    print("\n1️⃣ 每小时报表消息格式:")
    print("-" * 40)
    hourly_message = notifier._build_hourly_report_message(sample_report)
    print(hourly_message)
    
    # 演示每日总结消息
    print("\n2️⃣ 每日总结消息格式:")
    print("-" * 40)
    daily_message = notifier._build_daily_summary_message(sample_report)
    print(daily_message)
    
    # 演示错误警报消息
    print("\n3️⃣ 错误警报消息格式:")
    print("-" * 40)
    error_info = {
        'message': '数据库连接超时，请检查网络连接',
        'timestamp': datetime.now().isoformat()
    }
    error_message = notifier._build_error_alert_message(error_info)
    print(error_message)


async def demo_scheduler_usage():
    """演示调度器使用方法"""
    print("\n" + "=" * 60)
    print("⏰ 调度器使用演示")
    print("=" * 60)
    
    # 初始化报表系统
    report_system = WinRateReportSystem("./data/signal_settlement.db")
    
    # 模拟通知回调函数
    async def demo_notification_callback(report, report_type="hourly_report"):
        print(f"   📨 模拟发送通知: {report_type}")
        print(f"      今日信号数: {report.get('summary', {}).get('today', {}).get('total_signals', 0)}")
        print(f"      今日胜率: {report.get('summary', {}).get('today', {}).get('win_rate', 0):.1f}%")
    
    # 创建调度器配置
    scheduler_config = SchedulerConfig(
        enable_daily_calculation=True,
        enable_hourly_reports=True,
        daily_calculation_time="00:00",
        hourly_report_interval=1,
        timezone="Asia/Shanghai",
        send_empty_reports=False,
        min_signals_for_report=1
    )
    
    print("\n1️⃣ 调度器配置:")
    print(f"   每日计算时间: {scheduler_config.daily_calculation_time}")
    print(f"   每小时报表间隔: {scheduler_config.hourly_report_interval}小时")
    print(f"   时区: {scheduler_config.timezone}")
    print(f"   最小信号数要求: {scheduler_config.min_signals_for_report}")
    
    # 初始化调度器
    scheduler = WinRateReportScheduler(
        report_system=report_system,
        config=scheduler_config,
        notification_callback=demo_notification_callback
    )
    
    print("\n2️⃣ 启动调度器:")
    scheduler.start()
    print(f"   调度器状态: {'运行中' if scheduler.is_running else '已停止'}")
    
    print("\n3️⃣ 手动触发任务:")
    
    # 强制执行每日计算
    print("   触发每日计算...")
    scheduler.force_daily_calculation()
    await asyncio.sleep(1)
    
    # 强制发送每小时报表
    print("   触发每小时报表...")
    scheduler.force_hourly_report()
    await asyncio.sleep(1)
    
    print("\n4️⃣ 调度器状态:")
    status = scheduler.get_scheduler_status()
    print(f"   运行状态: {status['is_running']}")
    print(f"   每日计算次数: {status['daily_calculation_count']}")
    print(f"   每小时报表次数: {status['hourly_report_count']}")
    print(f"   错误次数: {status['error_count']}")
    
    print("\n5️⃣ 停止调度器:")
    scheduler.stop()
    print(f"   调度器状态: {'运行中' if scheduler.is_running else '已停止'}")


def demo_configuration():
    """演示配置选项"""
    print("\n" + "=" * 60)
    print("⚙️ 配置选项演示")
    print("=" * 60)
    
    print("\n1️⃣ 调度器配置选项:")
    scheduler_config = SchedulerConfig()
    print(f"   启用每日计算: {scheduler_config.enable_daily_calculation}")
    print(f"   启用每小时报表: {scheduler_config.enable_hourly_reports}")
    print(f"   每日计算时间: {scheduler_config.daily_calculation_time}")
    print(f"   每小时间隔: {scheduler_config.hourly_report_interval}小时")
    print(f"   时区: {scheduler_config.timezone}")
    print(f"   最大工作线程: {scheduler_config.max_workers}")
    print(f"   发送空报表: {scheduler_config.send_empty_reports}")
    print(f"   最小信号数: {scheduler_config.min_signals_for_report}")
    
    print("\n2️⃣ 通知配置选项:")
    notification_config = WinRateNotificationConfig()
    print(f"   启用每小时报表: {notification_config.enable_hourly_reports}")
    print(f"   启用每日总结: {notification_config.enable_daily_summary}")
    print(f"   启用错误警报: {notification_config.enable_error_alerts}")
    print(f"   每小时最小信号数: {notification_config.min_signals_for_hourly}")
    print(f"   每日最小信号数: {notification_config.min_signals_for_daily}")
    print(f"   使用表情符号: {notification_config.use_emoji}")
    print(f"   显示详细统计: {notification_config.show_detailed_stats}")
    print(f"   显示趋势分析: {notification_config.show_trend_analysis}")
    
    print("\n3️⃣ 自定义配置示例:")
    custom_config = SchedulerConfig(
        enable_daily_calculation=True,
        enable_hourly_reports=True,
        daily_calculation_time="01:00",  # 凌晨1点计算
        hourly_report_interval=2,        # 每2小时发送一次
        send_empty_reports=False,        # 不发送空报表
        min_signals_for_report=3         # 至少3个信号才发送
    )
    print(f"   自定义每日计算时间: {custom_config.daily_calculation_time}")
    print(f"   自定义报表间隔: {custom_config.hourly_report_interval}小时")
    print(f"   自定义最小信号数: {custom_config.min_signals_for_report}")


async def main():
    """主演示函数"""
    print("🎯 胜率统计报表系统完整演示")
    print("=" * 80)
    
    try:
        # 确保数据库存在
        if not os.path.exists("./data/signal_settlement.db"):
            print("⚠️  警告: 信号结算数据库不存在，某些功能可能无法正常演示")
            print("   请先运行交易系统生成一些信号数据，或运行测试程序创建测试数据")
            print()
        
        # 1. 基本使用演示
        demo_basic_usage()
        
        # 2. 通知消息演示
        demo_notification_messages()
        
        # 3. 调度器使用演示
        await demo_scheduler_usage()
        
        # 4. 配置选项演示
        demo_configuration()
        
        print("\n" + "=" * 80)
        print("🎉 演示完成！")
        print("\n💡 使用提示:")
        print("   1. 修改 config/win_rate_report_config.json 配置钉钉Token")
        print("   2. 运行 python3 win_rate_report_main.py 启动完整系统")
        print("   3. 运行 python3 tests/test_win_rate_report_system.py 进行功能测试")
        print("   4. 查看 logs/win_rate_report.log 获取详细日志")
        print("   5. 导出的报表保存在 exports/ 目录下")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 确保必要的目录存在
    os.makedirs('./logs', exist_ok=True)
    os.makedirs('./exports', exist_ok=True)
    os.makedirs('./config', exist_ok=True)
    
    # 运行演示
    asyncio.run(main())
