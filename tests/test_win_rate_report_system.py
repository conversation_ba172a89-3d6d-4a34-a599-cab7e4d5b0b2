#!/usr/bin/env python3
"""
胜率统计报表系统测试程序
验证系统各个组件的功能和集成效果

Author: AI Assistant
Date: 2025-07-17
"""

import sys
import os
import asyncio
import tempfile
import shutil
import sqlite3
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.win_rate_report_system import WinRateReportSystem, WinRateStats
from quant.strategies.win_rate_report_scheduler import WinRateReportScheduler, SchedulerConfig
from quant.strategies.win_rate_report_notifier import WinRateReportNotifier, WinRateNotificationConfig


def create_test_database(db_path: str):
    """创建测试数据库并插入模拟数据"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建信号记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS signal_records (
            signal_id TEXT PRIMARY KEY,
            timestamp TEXT NOT NULL,
            direction TEXT NOT NULL,
            confidence REAL NOT NULL,
            signal_price REAL NOT NULL,
            expiry_time TEXT NOT NULL,
            result TEXT DEFAULT 'PENDING',
            settlement_price REAL,
            settlement_time TEXT,
            pnl REAL DEFAULT 0.0,
            auto_settled BOOLEAN DEFAULT FALSE,
            signal_strength TEXT DEFAULT 'MEDIUM',
            supporting_indicators TEXT,
            market_conditions TEXT DEFAULT 'NORMAL',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建结算统计表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS settlement_stats (
            date TEXT PRIMARY KEY,
            total_signals INTEGER DEFAULT 0,
            total_settled INTEGER DEFAULT 0,
            wins INTEGER DEFAULT 0,
            losses INTEGER DEFAULT 0,
            ties INTEGER DEFAULT 0,
            win_rate REAL DEFAULT 0.0,
            avg_confidence REAL DEFAULT 0.0,
            total_pnl REAL DEFAULT 0.0,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入测试数据
    test_signals = [
        # 今日信号
        ('signal_001', datetime.now().isoformat(), 'UP', 85.0, 95000.0, 
         (datetime.now() + timedelta(minutes=10)).isoformat(), 'WIN', 95500.0, 
         (datetime.now() + timedelta(minutes=10)).isoformat(), 0.53, True, 'STRONG'),
        
        ('signal_002', (datetime.now() - timedelta(hours=1)).isoformat(), 'DOWN', 78.0, 94800.0,
         (datetime.now() - timedelta(hours=1) + timedelta(minutes=10)).isoformat(), 'LOSS', 95100.0,
         (datetime.now() - timedelta(hours=1) + timedelta(minutes=10)).isoformat(), -0.32, True, 'MEDIUM'),
        
        ('signal_003', (datetime.now() - timedelta(hours=2)).isoformat(), 'UP', 92.0, 94500.0,
         (datetime.now() - timedelta(hours=2) + timedelta(minutes=10)).isoformat(), 'WIN', 95200.0,
         (datetime.now() - timedelta(hours=2) + timedelta(minutes=10)).isoformat(), 0.74, True, 'STRONG'),
        
        # 昨日信号
        ('signal_004', (datetime.now() - timedelta(days=1)).isoformat(), 'DOWN', 65.0, 96000.0,
         (datetime.now() - timedelta(days=1) + timedelta(minutes=10)).isoformat(), 'WIN', 95400.0,
         (datetime.now() - timedelta(days=1) + timedelta(minutes=10)).isoformat(), 0.63, True, 'WEAK'),
        
        ('signal_005', (datetime.now() - timedelta(days=1, hours=2)).isoformat(), 'UP', 88.0, 95800.0,
         (datetime.now() - timedelta(days=1, hours=2) + timedelta(minutes=10)).isoformat(), 'LOSS', 95200.0,
         (datetime.now() - timedelta(days=1, hours=2) + timedelta(minutes=10)).isoformat(), -0.63, True, 'STRONG'),
        
        # 本周信号
        ('signal_006', (datetime.now() - timedelta(days=3)).isoformat(), 'UP', 75.0, 93000.0,
         (datetime.now() - timedelta(days=3) + timedelta(minutes=10)).isoformat(), 'WIN', 93800.0,
         (datetime.now() - timedelta(days=3) + timedelta(minutes=10)).isoformat(), 0.86, True, 'MEDIUM'),
        
        # 待结算信号
        ('signal_007', (datetime.now() - timedelta(minutes=5)).isoformat(), 'UP', 80.0, 95300.0,
         (datetime.now() - timedelta(minutes=5) + timedelta(minutes=10)).isoformat(), 'PENDING', None,
         None, 0.0, False, 'MEDIUM'),
    ]
    
    for signal in test_signals:
        cursor.execute('''
            INSERT OR REPLACE INTO signal_records 
            (signal_id, timestamp, direction, confidence, signal_price, expiry_time, 
             result, settlement_price, settlement_time, pnl, auto_settled, signal_strength)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', signal)
    
    conn.commit()
    conn.close()
    print(f"✅ 测试数据库创建完成: {db_path}")


def test_win_rate_report_system():
    """测试胜率报表系统核心功能"""
    print("=" * 60)
    print("🧪 测试胜率报表系统核心功能")
    print("=" * 60)
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 创建测试数据
        create_test_database(db_path)
        
        # 初始化报表系统
        report_system = WinRateReportSystem(db_path)
        
        # 测试今日统计
        print("\n1️⃣ 测试今日胜率统计:")
        today_stats = report_system.calculate_win_rate_stats(1)
        print(f"   总信号数: {today_stats.total_signals}")
        print(f"   已结算数: {today_stats.total_settled}")
        print(f"   胜率: {today_stats.win_rate}%")
        print(f"   总盈亏: {today_stats.total_pnl}%")
        print(f"   看涨胜率: {today_stats.up_win_rate}%")
        print(f"   看跌胜率: {today_stats.down_win_rate}%")
        
        # 测试本周统计
        print("\n2️⃣ 测试本周胜率统计:")
        week_stats = report_system.calculate_win_rate_stats(7)
        print(f"   总信号数: {week_stats.total_signals}")
        print(f"   已结算数: {week_stats.total_settled}")
        print(f"   胜率: {week_stats.win_rate}%")
        print(f"   强信号胜率: {week_stats.strong_win_rate}%")
        
        # 测试趋势分析
        print("\n3️⃣ 测试趋势分析:")
        trend = report_system.analyze_trend(7)
        print(f"   最近表现: {trend.recent_performance}")
        print(f"   表现趋势: {trend.performance_trend}")
        print(f"   操作建议: {trend.recommendation}")
        print(f"   最大连胜: {trend.win_streak}")
        print(f"   最大连败: {trend.loss_streak}")
        
        # 测试综合报表
        print("\n4️⃣ 测试综合报表生成:")
        report = report_system.generate_comprehensive_report()
        print(f"   报表生成时间: {report['report_time']}")
        print(f"   今日胜率: {report['summary']['today']['win_rate']}%")
        print(f"   本周胜率: {report['summary']['week']['win_rate']}%")
        print(f"   一致性得分: {report['performance_metrics']['consistency']}")
        print(f"   信号质量: {report['performance_metrics']['signal_quality']}")
        
        # 测试数据导出
        print("\n5️⃣ 测试数据导出:")
        export_file = report_system.export_report_to_json(report)
        if export_file and os.path.exists(export_file):
            print(f"   ✅ 报表导出成功: {export_file}")
            os.remove(export_file)  # 清理测试文件
        else:
            print("   ❌ 报表导出失败")
        
        # 测试最近信号
        print("\n6️⃣ 测试最近信号获取:")
        recent_signals = report_system.get_recent_signals(3)
        print(f"   获取到 {len(recent_signals)} 个最近信号")
        for signal in recent_signals:
            print(f"   - {signal['signal_id']}: {signal['direction']} {signal['result']} ({signal['confidence']}%)")
        
        print("\n✅ 胜率报表系统核心功能测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.remove(db_path)


def test_notification_messages():
    """测试通知消息格式"""
    print("=" * 60)
    print("📱 测试通知消息格式")
    print("=" * 60)
    
    # 创建通知器
    config = WinRateNotificationConfig(
        dingtalk_token="test_token",
        use_emoji=True,
        show_detailed_stats=True,
        show_trend_analysis=True
    )
    notifier = WinRateReportNotifier(config)
    
    # 创建测试报表数据
    test_report = {
        'report_time': datetime.now().isoformat(),
        'summary': {
            'today': {
                'total_signals': 8,
                'settled_signals': 6,
                'win_rate': 66.7,
                'total_pnl': 1.25
            },
            'week': {
                'total_signals': 25,
                'settled_signals': 22,
                'win_rate': 63.6,
                'total_pnl': 3.45
            },
            'month': {
                'total_signals': 120,
                'settled_signals': 115,
                'win_rate': 58.3,
                'total_pnl': 12.8
            }
        },
        'detailed_stats': {
            'today': {
                'up_win_rate': 70.0,
                'down_win_rate': 60.0,
                'strong_win_rate': 80.0
            }
        },
        'trend_analysis': {
            'recent_performance': '最近10笔交易胜率70.0%',
            'performance_trend': '表现良好',
            'recommendation': '策略运行正常，可适当增加投注',
            'win_streak': 3,
            'loss_streak': 2
        },
        'performance_metrics': {
            'consistency': 75.5,
            'signal_quality': '良好'
        }
    }
    
    # 测试每小时报表消息
    print("\n1️⃣ 每小时报表消息:")
    hourly_message = notifier._build_hourly_report_message(test_report)
    print(hourly_message)
    
    # 测试每日总结消息
    print("\n2️⃣ 每日总结消息:")
    daily_message = notifier._build_daily_summary_message(test_report)
    print(daily_message)
    
    # 测试错误警报消息
    print("\n3️⃣ 错误警报消息:")
    error_info = {
        'message': '数据库连接失败',
        'timestamp': datetime.now().isoformat()
    }
    error_message = notifier._build_error_alert_message(error_info)
    print(error_message)
    
    print("\n✅ 通知消息格式测试完成")


async def test_scheduler_functionality():
    """测试调度器功能"""
    print("=" * 60)
    print("⏰ 测试调度器功能")
    print("=" * 60)
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 创建测试数据
        create_test_database(db_path)
        
        # 初始化组件
        report_system = WinRateReportSystem(db_path)
        
        # 模拟通知回调
        notification_calls = []
        
        async def mock_notification_callback(report, report_type="hourly_report"):
            notification_calls.append({
                'report_type': report_type,
                'timestamp': datetime.now().isoformat(),
                'report_summary': {
                    'today_signals': report.get('summary', {}).get('today', {}).get('total_signals', 0),
                    'today_win_rate': report.get('summary', {}).get('today', {}).get('win_rate', 0)
                }
            })
            print(f"   📨 模拟通知发送: {report_type}")
        
        # 创建调度器配置（测试用短间隔）
        scheduler_config = SchedulerConfig(
            enable_daily_calculation=True,
            enable_hourly_reports=True,
            daily_calculation_time="00:00",
            hourly_report_interval=1,
            timezone="Asia/Shanghai",
            max_workers=2
        )
        
        # 初始化调度器
        scheduler = WinRateReportScheduler(
            report_system=report_system,
            config=scheduler_config,
            notification_callback=mock_notification_callback
        )
        
        print("\n1️⃣ 测试调度器启动:")
        scheduler.start()
        print(f"   调度器运行状态: {scheduler.is_running}")
        
        print("\n2️⃣ 测试强制执行任务:")
        
        # 测试强制每日计算
        print("   执行强制每日计算...")
        scheduler.force_daily_calculation()
        await asyncio.sleep(2)  # 等待任务执行
        
        # 测试强制每小时报表
        print("   执行强制每小时报表...")
        scheduler.force_hourly_report()
        await asyncio.sleep(2)  # 等待任务执行
        
        print("\n3️⃣ 测试调度器状态:")
        status = scheduler.get_scheduler_status()
        print(f"   运行状态: {status['is_running']}")
        print(f"   每日计算次数: {status['daily_calculation_count']}")
        print(f"   每小时报表次数: {status['hourly_report_count']}")
        print(f"   错误次数: {status['error_count']}")
        
        print("\n4️⃣ 测试通知回调:")
        print(f"   总通知次数: {len(notification_calls)}")
        for i, call in enumerate(notification_calls, 1):
            print(f"   通知{i}: {call['report_type']} - 今日信号{call['report_summary']['today_signals']}个")
        
        print("\n5️⃣ 测试调度器停止:")
        scheduler.stop()
        print(f"   调度器运行状态: {scheduler.is_running}")
        
        print("\n✅ 调度器功能测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.remove(db_path)


async def test_integration():
    """集成测试"""
    print("=" * 60)
    print("🔗 集成测试")
    print("=" * 60)
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 创建测试数据
        create_test_database(db_path)
        
        print("\n1️⃣ 初始化完整系统:")
        
        # 初始化报表系统
        report_system = WinRateReportSystem(db_path)
        print("   ✅ 报表系统初始化完成")
        
        # 初始化通知器
        notification_config = WinRateNotificationConfig(
            dingtalk_token="test_token",
            enable_hourly_reports=True,
            enable_daily_summary=True,
            enable_error_alerts=True
        )
        notifier = WinRateReportNotifier(notification_config)
        print("   ✅ 通知器初始化完成")
        
        print("\n2️⃣ 测试完整工作流程:")
        
        # 生成报表
        print("   生成综合报表...")
        report = report_system.generate_comprehensive_report()
        
        # 模拟发送通知（不实际发送到钉钉）
        with patch('quant.utils.dingtalk.Dingtalk.markdown') as mock_dingtalk:
            mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
            
            # 发送每小时报表
            print("   发送每小时报表...")
            success, error = await notifier.send_hourly_report(report)
            print(f"   每小时报表发送结果: {'成功' if success else '失败'}")
            
            # 发送每日总结
            print("   发送每日总结...")
            success, error = await notifier.send_daily_summary(report)
            print(f"   每日总结发送结果: {'成功' if success else '失败'}")
            
            # 发送错误警报
            print("   发送错误警报...")
            error_info = {'message': '测试错误警报', 'timestamp': datetime.now().isoformat()}
            success, error = await notifier.send_error_alert(error_info)
            print(f"   错误警报发送结果: {'成功' if success else '失败'}")
        
        print("\n3️⃣ 验证数据一致性:")
        
        # 验证统计数据
        today_stats = report_system.calculate_win_rate_stats(1)
        week_stats = report_system.calculate_win_rate_stats(7)
        
        print(f"   今日信号数: {today_stats.total_signals}")
        print(f"   本周信号数: {week_stats.total_signals}")
        print(f"   数据一致性: {'✅ 正常' if week_stats.total_signals >= today_stats.total_signals else '❌ 异常'}")
        
        # 验证通知统计
        notification_stats = notifier.get_notification_stats()
        print(f"   发送通知次数: {notification_stats['sent_reports_count']}")
        
        print("\n✅ 集成测试完成")
        
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.remove(db_path)


async def main():
    """主测试函数"""
    print("🚀 胜率统计报表系统测试开始")
    print("=" * 80)
    
    try:
        # 1. 测试核心功能
        test_win_rate_report_system()
        
        # 2. 测试通知消息
        test_notification_messages()
        
        # 3. 测试调度器
        await test_scheduler_functionality()
        
        # 4. 集成测试
        await test_integration()
        
        print("\n" + "=" * 80)
        print("🎉 所有测试完成！系统功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 确保必要的目录存在
    os.makedirs('./logs', exist_ok=True)
    os.makedirs('./exports', exist_ok=True)

    # 运行测试
    asyncio.run(main())
